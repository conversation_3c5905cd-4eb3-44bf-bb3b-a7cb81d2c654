{"info": {"name": "Stock Movements Management API", "description": "مجموعة أمثلة لاختبار API إدارة حركة المخزون", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Stock Movements", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/movements", "host": ["{{baseUrl}}"], "path": ["api", "movements"]}}}, {"name": "Get Movements with Pagination", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/movements?page=1&limit=5", "host": ["{{baseUrl}}"], "path": ["api", "movements"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "5"}]}}}, {"name": "Get Movements by Product", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/movements?product_id=50", "host": ["{{baseUrl}}"], "path": ["api", "movements"], "query": [{"key": "product_id", "value": "50"}]}}}, {"name": "Get Movements by Warehouse", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/movements?warehouse_id=11", "host": ["{{baseUrl}}"], "path": ["api", "movements"], "query": [{"key": "warehouse_id", "value": "11"}]}}}, {"name": "Get IN Movements Only", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/movements?type=IN", "host": ["{{baseUrl}}"], "path": ["api", "movements"], "query": [{"key": "type", "value": "IN"}]}}}, {"name": "Get OUT Movements Only", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/movements?type=OUT", "host": ["{{baseUrl}}"], "path": ["api", "movements"], "query": [{"key": "type", "value": "OUT"}]}}}, {"name": "Get Movements by Date Range", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/movements?start_date=2024-12-01&end_date=2024-12-31", "host": ["{{baseUrl}}"], "path": ["api", "movements"], "query": [{"key": "start_date", "value": "2024-12-01"}, {"key": "end_date", "value": "2024-12-31"}]}}}, {"name": "Get Movement by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/movements/1", "host": ["{{baseUrl}}"], "path": ["api", "movements", "1"]}}}, {"name": "Create IN Movement - Stock Receipt", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 50,\n  \"warehouse_id\": 11,\n  \"type\": \"IN\",\n  \"quantity\": 20,\n  \"reference\": \"PO-2024-005\",\n  \"description\": \"استلام شحنة جديدة من المورد الرئيسي\",\n  \"moved_at\": \"2024-12-27T10:00:00.000Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/movements", "host": ["{{baseUrl}}"], "path": ["api", "movements"]}}}, {"name": "Create OUT Movement - Sale", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 50,\n  \"warehouse_id\": 11,\n  \"type\": \"OUT\",\n  \"quantity\": 3,\n  \"reference\": \"SO-2024-010\",\n  \"description\": \"بيع للعميل - فاتورة رقم 2001\"\n}"}, "url": {"raw": "{{baseUrl}}/api/movements", "host": ["{{baseUrl}}"], "path": ["api", "movements"]}}}, {"name": "Create IN Movement - Transfer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 51,\n  \"warehouse_id\": 12,\n  \"type\": \"IN\",\n  \"quantity\": 10,\n  \"reference\": \"TR-2024-001\",\n  \"description\": \"تحويل من المستودع الرئيسي\"\n}"}, "url": {"raw": "{{baseUrl}}/api/movements", "host": ["{{baseUrl}}"], "path": ["api", "movements"]}}}, {"name": "Create OUT Movement - Damage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 52,\n  \"warehouse_id\": 12,\n  \"type\": \"OUT\",\n  \"quantity\": 2,\n  \"reference\": \"DMG-2024-001\",\n  \"description\": \"إتلاف بضاعة تالفة\"\n}"}, "url": {"raw": "{{baseUrl}}/api/movements", "host": ["{{baseUrl}}"], "path": ["api", "movements"]}}}, {"name": "Delete Movement", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/movements/1", "host": ["{{baseUrl}}"], "path": ["api", "movements", "1"]}}}, {"name": "Get Stock Movements Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/movements/stats", "host": ["{{baseUrl}}"], "path": ["api", "movements", "stats"]}}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}