const express = require('express');
const router = express.Router();
const purchaseOrderController = require('../controllers/purchaseOrder.controller');

// Middleware for request logging
const requestLogger = (req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.originalUrl}`);
  next();
};

// Apply logging middleware to all routes
router.use(requestLogger);

// Validation middleware for purchase order creation
const validatePurchaseOrderData = (req, res, next) => {
  const { supplier_id, items } = req.body;
  
  // Check required fields for POST requests
  if (req.method === 'POST') {
    if (!supplier_id) {
      return res.status(400).json({
        success: false,
        message: 'معرف المورد مطلوب',
        timestamp: new Date().toISOString()
      });
    }
    
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'عناصر الطلب مطلوبة ويجب أن تكون مصفوفة غير فارغة',
        timestamp: new Date().toISOString()
      });
    }
    
    // Validate each item
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      
      if (!item.product_id || typeof item.product_id !== 'number') {
        return res.status(400).json({
          success: false,
          message: `معرف المنتج مطلوب للعنصر ${i + 1}`,
          timestamp: new Date().toISOString()
        });
      }
      
      if (!item.quantity_ordered || typeof item.quantity_ordered !== 'number' || item.quantity_ordered < 1) {
        return res.status(400).json({
          success: false,
          message: `الكمية المطلوبة يجب أن تكون رقم أكبر من 0 للعنصر ${i + 1}`,
          timestamp: new Date().toISOString()
        });
      }
      
      if (item.unit_price !== undefined && (typeof item.unit_price !== 'number' || item.unit_price < 0)) {
        return res.status(400).json({
          success: false,
          message: `سعر الوحدة يجب أن يكون رقم أكبر من أو يساوي 0 للعنصر ${i + 1}`,
          timestamp: new Date().toISOString()
        });
      }
    }
  }
  
  // Validate supplier_id if provided
  if (supplier_id && (typeof supplier_id !== 'number' || supplier_id <= 0)) {
    return res.status(400).json({
      success: false,
      message: 'معرف المورد يجب أن يكون رقم صحيح أكبر من 0',
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

// Validation middleware for receiving products
const validateReceiveData = (req, res, next) => {
  const { items } = req.body;
  
  if (!items || !Array.isArray(items) || items.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'عناصر الاستلام مطلوبة ويجب أن تكون مصفوفة غير فارغة',
      timestamp: new Date().toISOString()
    });
  }
  
  // Validate each item
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    
    if (!item.product_id || typeof item.product_id !== 'number') {
      return res.status(400).json({
        success: false,
        message: `معرف المنتج مطلوب للعنصر ${i + 1}`,
        timestamp: new Date().toISOString()
      });
    }
    
    if (!item.quantity_received || typeof item.quantity_received !== 'number' || item.quantity_received < 1) {
      return res.status(400).json({
        success: false,
        message: `الكمية المستلمة يجب أن تكون رقم أكبر من 0 للعنصر ${i + 1}`,
        timestamp: new Date().toISOString()
      });
    }
  }
  
  next();
};

// Validation middleware for ID parameter
const validateIdParam = (req, res, next) => {
  const { id } = req.params;
  
  if (!id || isNaN(parseInt(id))) {
    return res.status(400).json({
      success: false,
      message: 'معرف أمر الشراء يجب أن يكون رقم صحيح',
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

// Routes

/**
 * @route   GET /api/purchase-orders
 * @desc    Get all purchase orders with optional filtering and pagination
 * @access  Public
 * @params  ?page=1&limit=10&supplier_id=1&status=NEW&start_date=2024-01-01&end_date=2024-12-31
 */
router.get('/', purchaseOrderController.getAllPurchaseOrders);

/**
 * @route   GET /api/purchase-orders/stats
 * @desc    Get purchase orders statistics
 * @access  Public
 */
router.get('/stats', purchaseOrderController.getPurchaseOrdersStats);

/**
 * @route   GET /api/purchase-orders/:id
 * @desc    Get single purchase order by ID
 * @access  Public
 */
router.get('/:id', validateIdParam, purchaseOrderController.getPurchaseOrderById);

/**
 * @route   POST /api/purchase-orders
 * @desc    Create new purchase order
 * @access  Public
 * @body    { supplier_id, delivery_date?, notes?, items: [{ product_id, quantity_ordered, unit_price? }] }
 */
router.post('/', validatePurchaseOrderData, purchaseOrderController.createPurchaseOrder);

/**
 * @route   PUT /api/purchase-orders/:id
 * @desc    Update purchase order by ID
 * @access  Public
 * @body    { supplier_id?, status?, delivery_date?, notes?, total_amount? }
 */
router.put('/:id', validateIdParam, purchaseOrderController.updatePurchaseOrder);

/**
 * @route   DELETE /api/purchase-orders/:id
 * @desc    Delete purchase order by ID (only NEW orders)
 * @access  Public
 */
router.delete('/:id', validateIdParam, purchaseOrderController.deletePurchaseOrder);

/**
 * @route   POST /api/purchase-orders/:id/receive
 * @desc    Receive products for purchase order and update inventory
 * @access  Public
 * @body    { items: [{ product_id, quantity_received }], warehouse_id? }
 */
router.post('/:id/receive', validateIdParam, validateReceiveData, purchaseOrderController.receiveProducts);

module.exports = router;
