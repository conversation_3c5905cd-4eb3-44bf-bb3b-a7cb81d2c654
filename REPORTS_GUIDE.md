# دليل التقارير ولوحة التحكم
## Reports & Dashboard Guide

هذا الدليل يوضح كيفية استخدام وحدة التقارير ولوحة التحكم في نظام إدارة المخزون.

---

## 📊 المؤشرات الرئيسية للأداء (KPIs)

### المسار: `GET /api/reports/kpis`

يوفر نظرة شاملة على أداء النظام:

```json
{
  "success": true,
  "data": {
    "kpis": {
      "inventory": {
        "totalProducts": 12,
        "totalInventoryQuantity": 150,
        "lowStockProducts": 3,
        "outOfStockProducts": 2,
        "totalWarehouses": 5
      },
      "suppliers": {
        "totalSuppliers": 10
      },
      "orders": {
        "purchase": {
          "total": 15,
          "active": 8,
          "totalValue": 45000.00
        },
        "sales": {
          "total": 20,
          "active": 12,
          "totalValue": 38500.00
        }
      },
      "movements": {
        "total": 45,
        "inbound": 25,
        "outbound": 20
      },
      "financial": {
        "totalRevenue": 38500.00,
        "totalPurchases": 45000.00,
        "grossProfit": -6500.00
      }
    }
  }
}
```

---

## 📈 اتجاه المبيعات

### المسار: `GET /api/reports/sales-trend`

يعرض بيانات المبيعات لآخر 6 أشهر:

```json
{
  "success": true,
  "data": {
    "trend": [
      {
        "month": "يوليو 2024",
        "orders_count": 5,
        "total_revenue": 12500.00,
        "average_order_value": 2500.00
      },
      {
        "month": "أغسطس 2024",
        "orders_count": 8,
        "total_revenue": 18000.00,
        "average_order_value": 2250.00
      }
    ],
    "period": "آخر 6 أشهر"
  }
}
```

**استخدامات:**
- رسوم بيانية خطية للمبيعات
- تحليل الاتجاهات الموسمية
- مقارنة الأداء الشهري

---

## 🏆 المنتجات الأكثر مبيعاً

### المسار: `GET /api/reports/top-products?limit=5`

```json
{
  "success": true,
  "data": {
    "products": [
      {
        "product_id": 1,
        "product_name": "لابتوب ديل انسبايرون 15",
        "product_code": "DELL-INS-15",
        "unit": "قطعة",
        "total_ordered": 25,
        "total_delivered": 20,
        "orders_count": 8,
        "total_revenue": 50000.00,
        "delivery_rate": 80
      }
    ],
    "limit": 5
  }
}
```

**استخدامات:**
- رسوم بيانية دائرية أو أعمدة
- تحديد المنتجات الأكثر ربحية
- تخطيط المخزون

---

## 🏢 الموردين الأعلى توريداً

### المسار: `GET /api/reports/top-suppliers?limit=5`

```json
{
  "success": true,
  "data": {
    "suppliers": [
      {
        "supplier_id": 1,
        "supplier_name": "شركة التقنية المتقدمة",
        "contact_name": "أحمد محمد",
        "phone": "+966501234567",
        "email": "<EMAIL>",
        "orders_count": 12,
        "total_value": 85000.00,
        "average_order_value": 7083.33
      }
    ],
    "limit": 5
  }
}
```

**استخدامات:**
- تقييم أداء الموردين
- تحليل التكاليف
- اتخاذ قرارات الشراء

---

## 🚨 تنبيهات المخزون

### المسار: `GET /api/reports/inventory-alerts`

```json
{
  "success": true,
  "data": {
    "alerts": {
      "low_stock": {
        "count": 3,
        "items": [
          {
            "inventory_id": 5,
            "product": {
              "id": 3,
              "name": "كيبورد ميكانيكي للألعاب",
              "code": "KB-GAMING-01",
              "unit": "قطعة"
            },
            "warehouse": {
              "id": 1,
              "name": "المستودع الرئيسي - الرياض",
              "code": "WH-RYD-01",
              "location": "الرياض"
            },
            "current_quantity": 2,
            "minimum_quantity": 5,
            "shortage": 3,
            "stock_level_percentage": 40,
            "alert_level": "medium"
          }
        ]
      },
      "out_of_stock": {
        "count": 2,
        "items": [
          {
            "inventory_id": 8,
            "product": {
              "id": 8,
              "name": "طابعة HP LaserJet",
              "code": "HP-LJ-P1102",
              "unit": "قطعة"
            },
            "warehouse": {
              "id": 2,
              "name": "مستودع جدة التجاري",
              "code": "WH-JED-01",
              "location": "جدة"
            },
            "minimum_quantity": 2,
            "last_checked_at": "2024-12-27T10:30:00.000Z",
            "alert_level": "critical"
          }
        ]
      },
      "total_alerts": 5
    }
  }
}
```

**مستويات التنبيه:**
- `critical`: نفاد المخزون (كمية = 0)
- `high`: أقل من 50% من الحد الأدنى
- `medium`: بين 50% و 100% من الحد الأدنى

---

## 📋 حالة الطلبات

### المسار: `GET /api/reports/orders-status`

```json
{
  "success": true,
  "data": {
    "orders_status": {
      "purchase_orders": {
        "total": 15,
        "status_distribution": [
          {
            "status": "NEW",
            "status_arabic": "جديد",
            "count": 5,
            "total_value": 15000.00,
            "percentage": 33
          },
          {
            "status": "COMPLETED",
            "status_arabic": "مكتمل",
            "count": 8,
            "total_value": 25000.00,
            "percentage": 53
          }
        ],
        "pending_items": 12
      },
      "sales_orders": {
        "total": 20,
        "status_distribution": [
          {
            "status": "NEW",
            "status_arabic": "جديد",
            "count": 8,
            "total_value": 18000.00,
            "percentage": 40
          }
        ],
        "pending_items": 15
      },
      "summary": {
        "total_orders": 35,
        "total_pending_items": 27
      }
    }
  }
}
```

---

## 🎛️ لوحة التحكم الكاملة

### المسار: `GET /api/reports/dashboard`

يجمع جميع التقارير في استدعاء واحد لتحسين الأداء:

```json
{
  "success": true,
  "data": {
    "kpis": { /* بيانات KPIs */ },
    "sales_trend": [ /* بيانات اتجاه المبيعات */ ],
    "top_products": [ /* المنتجات الأكثر مبيعاً */ ],
    "inventory_alerts": { /* تنبيهات المخزون */ },
    "orders_status": { /* حالة الطلبات */ }
  }
}
```

---

## 🎨 استخدام البيانات في الواجهة الأمامية

### رسوم بيانية مقترحة:

1. **KPIs Cards**: عرض المؤشرات الرئيسية في بطاقات
2. **Line Chart**: اتجاه المبيعات الشهرية
3. **Bar Chart**: المنتجات الأكثر مبيعاً
4. **Pie Chart**: توزيع حالات الطلبات
5. **Alert List**: قائمة تنبيهات المخزون مع ألوان حسب المستوى

### مكتبات مقترحة:
- **Chart.js**: للرسوم البيانية
- **D3.js**: للتصورات المتقدمة
- **Recharts**: لـ React
- **ApexCharts**: متعددة المنصات

---

## 🔄 تحديث البيانات

- البيانات محدثة في الوقت الفعلي
- لا حاجة لتخزين مؤقت معقد
- يمكن إضافة تحديث تلقائي كل 30 ثانية

---

## 🚀 الاستخدام مع Postman

1. استورد ملف `postman-reports.json`
2. تأكد من تشغيل الخادم على `http://localhost:3000`
3. جرب جميع التقارير
4. استخدم البيانات لبناء لوحة التحكم

---

## 📱 أمثلة للتطبيق

```javascript
// جلب بيانات لوحة التحكم
const fetchDashboardData = async () => {
  try {
    const response = await fetch('/api/reports/dashboard');
    const data = await response.json();
    
    if (data.success) {
      updateKPIs(data.data.kpis);
      updateSalesChart(data.data.sales_trend);
      updateTopProducts(data.data.top_products);
      updateAlerts(data.data.inventory_alerts);
    }
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
  }
};

// تحديث المؤشرات
const updateKPIs = (kpis) => {
  document.getElementById('total-products').textContent = kpis.inventory.totalProducts;
  document.getElementById('total-revenue').textContent = kpis.financial.totalRevenue.toLocaleString();
  document.getElementById('low-stock-alerts').textContent = kpis.inventory.lowStockProducts;
};
```

هذه الوحدة توفر أساساً قوياً لبناء لوحات تحكم تفاعلية ومفيدة لإدارة المخزون! 📊
