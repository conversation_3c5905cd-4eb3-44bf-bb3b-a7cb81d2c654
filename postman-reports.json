{"info": {"name": "Reports & Dashboard API", "description": "مجموعة أمثلة لاختبار API التقارير ولوحة التحكم", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get Main KPIs", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reports/kpis", "host": ["{{baseUrl}}"], "path": ["api", "reports", "kpis"]}, "description": "جلب المؤشرات الرئيسية للأداء (KPIs) - ع<PERSON><PERSON> المنتجات، الموردين، المستودعات، الطلبات، والبيانات المالية"}}, {"name": "Get Sales Trend (6 Months)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reports/sales-trend", "host": ["{{baseUrl}}"], "path": ["api", "reports", "sales-trend"]}, "description": "جلب اتجاه المبيعات لآخر 6 أشهر مع البيانات الشهرية للإيرادات وعدد الطلبات"}}, {"name": "Get Top 5 Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reports/top-products", "host": ["{{baseUrl}}"], "path": ["api", "reports", "top-products"]}, "description": "جلب أفضل 5 منتجات مبيعاً مع الكميات والإيرادات"}}, {"name": "Get Top 10 Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reports/top-products?limit=10", "host": ["{{baseUrl}}"], "path": ["api", "reports", "top-products"], "query": [{"key": "limit", "value": "10"}]}, "description": "جلب أفضل 10 منتجات مبيعاً"}}, {"name": "Get Top 5 Suppliers", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reports/top-suppliers", "host": ["{{baseUrl}}"], "path": ["api", "reports", "top-suppliers"]}, "description": "جل<PERSON> أفضل 5 موردين حسب قيمة المشتريات"}}, {"name": "Get Top 10 Suppliers", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reports/top-suppliers?limit=10", "host": ["{{baseUrl}}"], "path": ["api", "reports", "top-suppliers"], "query": [{"key": "limit", "value": "10"}]}, "description": "جل<PERSON> أفضل 10 موردين حسب قيمة المشتريات"}}, {"name": "Get Inventory Alerts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reports/inventory-alerts", "host": ["{{baseUrl}}"], "path": ["api", "reports", "inventory-alerts"]}, "description": "جلب تنبيهات المخزون - المنتجات قليلة المخزون والمنتجات نافدة المخزون"}}, {"name": "Get Orders Status Distribution", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reports/orders-status", "host": ["{{baseUrl}}"], "path": ["api", "reports", "orders-status"]}, "description": "جلب توزيع حالات الطلبات (أوامر الشراء والبيع) مع النسب المئوية"}}, {"name": "Get Complete Dashboard Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reports/dashboard", "host": ["{{baseUrl}}"], "path": ["api", "reports", "dashboard"]}, "description": "جلب بيانات لوحة التحكم الكاملة - يجمع جميع التقارير في استدعاء واحد"}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set timestamp for request tracking", "pm.globals.set('requestTimestamp', new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Basic response validation", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "});", "", "pm.test('Response has data field when successful', function () {", "    const jsonData = pm.response.json();", "    if (jsonData.success) {", "        pm.expect(jsonData).to.have.property('data');", "    }", "});", "", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});"]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}