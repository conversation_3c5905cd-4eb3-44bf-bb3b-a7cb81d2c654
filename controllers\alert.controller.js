const { InventoryAlert, Product, Warehouse, Inventory, sequelize } = require('../models');
const { Op } = require('sequelize');

// Helper function for error response
const sendErrorResponse = (res, statusCode, message, error = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (error && process.env.NODE_ENV === 'development') {
    response.error = error.message;
  }
  
  return res.status(statusCode).json(response);
};

// Helper function for success response
const sendSuccessResponse = (res, statusCode, message, data = null) => {
  const response = {
    success: true,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (data !== null) {
    response.data = data;
  }
  
  return res.status(statusCode).json(response);
};

// Get all alerts
exports.getAllAlerts = async (req, res) => {
  try {
    const { page = 1, limit = 10, level, status, type } = req.query;
    const offset = (page - 1) * limit;
    
    // Build where clause
    const whereClause = {};
    
    if (level) {
      whereClause.level = level;
    }
    
    if (status) {
      whereClause.status = status;
    }
    
    if (type) {
      whereClause.type = type;
    }
    
    const { count, rows } = await InventoryAlert.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'code', 'unit', 'minimum_quantity']
        },
        {
          model: Warehouse,
          as: 'warehouse',
          attributes: ['id', 'name', 'code', 'location']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [
        ['level', 'DESC'], // CRITICAL first
        ['created_at', 'DESC'] // Newest first
      ]
    });
    
    const totalPages = Math.ceil(count / limit);
    
    // Add additional info to each alert
    const enrichedAlerts = rows.map(alert => ({
      ...alert.toJSON(),
      age_hours: alert.getAgeInHours(),
      priority_score: alert.getPriorityScore(),
      is_critical: alert.isCritical(),
      shortage: alert.minimum_quantity - alert.quantity
    }));
    
    sendSuccessResponse(res, 200, 'تم جلب التنبيهات بنجاح', {
      alerts: enrichedAlerts,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error in getAllAlerts:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب التنبيهات', error);
  }
};

// Get active alerts only
exports.getActiveAlerts = async (req, res) => {
  try {
    const activeAlerts = await InventoryAlert.getActiveAlerts();
    
    // Add additional info to each alert
    const enrichedAlerts = activeAlerts.map(alert => ({
      ...alert.toJSON(),
      age_hours: alert.getAgeInHours(),
      priority_score: alert.getPriorityScore(),
      is_critical: alert.isCritical(),
      shortage: alert.minimum_quantity - alert.quantity
    }));
    
    // Group by level for better organization
    const groupedAlerts = {
      critical: enrichedAlerts.filter(alert => alert.level === 'CRITICAL'),
      high: enrichedAlerts.filter(alert => alert.level === 'HIGH'),
      medium: enrichedAlerts.filter(alert => alert.level === 'MEDIUM')
    };
    
    sendSuccessResponse(res, 200, 'تم جلب التنبيهات النشطة بنجاح', {
      alerts: enrichedAlerts,
      grouped_alerts: groupedAlerts,
      total_count: enrichedAlerts.length,
      critical_count: groupedAlerts.critical.length,
      high_count: groupedAlerts.high.length,
      medium_count: groupedAlerts.medium.length
    });
  } catch (error) {
    console.error('Error in getActiveAlerts:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب التنبيهات النشطة', error);
  }
};

// Get single alert by ID
exports.getAlertById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const alert = await InventoryAlert.findByPk(id, {
      include: [
        {
          model: Product,
          as: 'product'
        },
        {
          model: Warehouse,
          as: 'warehouse'
        }
      ]
    });
    
    if (!alert) {
      return sendErrorResponse(res, 404, 'التنبيه غير موجود');
    }
    
    // Mark as seen if it's new
    if (alert.status === 'NEW') {
      await alert.update({ status: 'SEEN' });
    }
    
    const enrichedAlert = {
      ...alert.toJSON(),
      age_hours: alert.getAgeInHours(),
      priority_score: alert.getPriorityScore(),
      is_critical: alert.isCritical(),
      shortage: alert.minimum_quantity - alert.quantity
    };
    
    sendSuccessResponse(res, 200, 'تم جلب التنبيه بنجاح', { alert: enrichedAlert });
  } catch (error) {
    console.error('Error in getAlertById:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب التنبيه', error);
  }
};

// Mark alert as seen
exports.markAsSeen = async (req, res) => {
  try {
    const { id } = req.params;
    
    const alert = await InventoryAlert.findByPk(id);
    
    if (!alert) {
      return sendErrorResponse(res, 404, 'التنبيه غير موجود');
    }
    
    if (alert.status === 'RESOLVED') {
      return sendErrorResponse(res, 400, 'لا يمكن تعديل تنبيه محلول');
    }
    
    await alert.update({ status: 'SEEN' });
    
    sendSuccessResponse(res, 200, 'تم تحديث حالة التنبيه إلى مشاهد', { alert });
  } catch (error) {
    console.error('Error in markAsSeen:', error);
    sendErrorResponse(res, 500, 'خطأ في تحديث حالة التنبيه', error);
  }
};

// Resolve alert
exports.resolveAlert = async (req, res) => {
  try {
    const { id } = req.params;
    const { resolution_note } = req.body;
    
    const alert = await InventoryAlert.findByPk(id, {
      include: [
        {
          model: Product,
          as: 'product'
        },
        {
          model: Warehouse,
          as: 'warehouse'
        }
      ]
    });
    
    if (!alert) {
      return sendErrorResponse(res, 404, 'التنبيه غير موجود');
    }
    
    if (alert.status === 'RESOLVED') {
      return sendErrorResponse(res, 400, 'التنبيه محلول مسبقاً');
    }
    
    // Update alert message with resolution note
    let updatedMessage = alert.alert_message;
    if (resolution_note) {
      updatedMessage += ` - تم الحل: ${resolution_note}`;
    }
    
    await alert.update({
      status: 'RESOLVED',
      alert_message: updatedMessage
    });
    
    sendSuccessResponse(res, 200, 'تم حل التنبيه بنجاح', { alert });
  } catch (error) {
    console.error('Error in resolveAlert:', error);
    sendErrorResponse(res, 500, 'خطأ في حل التنبيه', error);
  }
};

// Get alert statistics
exports.getAlertStats = async (req, res) => {
  try {
    const stats = await InventoryAlert.getAlertStats();

    // Get additional statistics
    const totalAlerts = await InventoryAlert.count();
    const alertsToday = await InventoryAlert.count({
      where: {
        created_at: {
          [Op.gte]: new Date(new Date().setHours(0, 0, 0, 0))
        }
      }
    });

    const alertsThisWeek = await InventoryAlert.count({
      where: {
        created_at: {
          [Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        }
      }
    });

    // Get most problematic products
    const problematicProducts = await InventoryAlert.findAll({
      attributes: [
        'product_id',
        [sequelize.fn('COUNT', sequelize.col('InventoryAlert.id')), 'alert_count'],
        [sequelize.fn('MAX', sequelize.col('created_at')), 'latest_alert']
      ],
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['name', 'code']
        }
      ],
      where: {
        created_at: {
          [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      },
      group: ['product_id', 'product.id'],
      order: [[sequelize.fn('COUNT', sequelize.col('InventoryAlert.id')), 'DESC']],
      limit: 5,
      raw: false
    });

    const enrichedStats = {
      ...stats,
      total_alerts: totalAlerts,
      alerts_today: alertsToday,
      alerts_this_week: alertsThisWeek,
      most_problematic_products: problematicProducts.map(item => ({
        product_id: item.product_id,
        product_name: item.product.name,
        product_code: item.product.code,
        alert_count: parseInt(item.dataValues.alert_count),
        latest_alert: item.dataValues.latest_alert
      }))
    };

    sendSuccessResponse(res, 200, 'تم جلب إحصائيات التنبيهات بنجاح', { stats: enrichedStats });
  } catch (error) {
    console.error('Error in getAlertStats:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب إحصائيات التنبيهات', error);
  }
};

// Bulk resolve alerts
exports.bulkResolveAlerts = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { alert_ids, resolution_note } = req.body;

    if (!alert_ids || !Array.isArray(alert_ids) || alert_ids.length === 0) {
      await transaction.rollback();
      return sendErrorResponse(res, 400, 'معرفات التنبيهات مطلوبة');
    }

    const alerts = await InventoryAlert.findAll({
      where: {
        id: { [Op.in]: alert_ids },
        status: { [Op.ne]: 'RESOLVED' }
      },
      transaction
    });

    if (alerts.length === 0) {
      await transaction.rollback();
      return sendErrorResponse(res, 404, 'لا توجد تنبيهات قابلة للحل');
    }

    // Update all alerts
    const updatePromises = alerts.map(alert => {
      let updatedMessage = alert.alert_message;
      if (resolution_note) {
        updatedMessage += ` - تم الحل: ${resolution_note}`;
      }

      return alert.update({
        status: 'RESOLVED',
        alert_message: updatedMessage
      }, { transaction });
    });

    await Promise.all(updatePromises);
    await transaction.commit();

    sendSuccessResponse(res, 200, `تم حل ${alerts.length} تنبيه بنجاح`, {
      resolved_count: alerts.length,
      resolved_alerts: alerts.map(alert => alert.id)
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error in bulkResolveAlerts:', error);
    sendErrorResponse(res, 500, 'خطأ في حل التنبيهات', error);
  }
};

// Check and create alerts for all inventory items
exports.checkAllInventoryAlerts = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    // Get all inventory items with their product minimum quantities
    const inventoryItems = await Inventory.findAll({
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'code', 'minimum_quantity']
        },
        {
          model: Warehouse,
          as: 'warehouse',
          attributes: ['id', 'name', 'code']
        }
      ],
      transaction
    });

    let alertsCreated = 0;
    let alertsResolved = 0;

    for (const inventory of inventoryItems) {
      const minimumQuantity = inventory.product.minimum_quantity || 5;

      const alert = await InventoryAlert.createSmartAlert(
        inventory.product_id,
        inventory.warehouse_id,
        inventory.quantity,
        minimumQuantity,
        transaction
      );

      if (alert) {
        if (alert.status === 'NEW') {
          alertsCreated++;
        } else if (alert.status === 'RESOLVED') {
          alertsResolved++;
        }
      }
    }

    await transaction.commit();

    sendSuccessResponse(res, 200, 'تم فحص جميع عناصر المخزون', {
      total_items_checked: inventoryItems.length,
      alerts_created: alertsCreated,
      alerts_resolved: alertsResolved,
      message: `تم فحص ${inventoryItems.length} عنصر، إنشاء ${alertsCreated} تنبيه جديد، وحل ${alertsResolved} تنبيه`
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error in checkAllInventoryAlerts:', error);
    sendErrorResponse(res, 500, 'خطأ في فحص تنبيهات المخزون', error);
  }
};
