import { useState, useEffect } from 'react';
import { 
  Package, 
  Users, 
  Warehouse, 
  AlertTriangle, 
  TrendingUp, 
  TrendingDown,
  ShoppingCart,
  DollarSign,
  Activity,
  Eye,
  Plus,
  RefreshCw
} from 'lucide-react';
import { dashboardService } from '../services/dashboardService';

const Dashboard = () => {
  const [kpis, setKpis] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const data = await dashboardService.getKPIs();
      setKpis(data);
      setLastUpdated(new Date());
      setError(null);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('فشل في تحميل بيانات لوحة التحكم');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
    // تحديث البيانات كل 5 دقائق
    const interval = setInterval(fetchDashboardData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const KPICard = ({ title, value, icon: Icon, color, subtitle, trend, trendValue }) => {
    const colorClasses = {
      blue: 'bg-blue-50 border-blue-200 text-blue-600',
      green: 'bg-green-50 border-green-200 text-green-600',
      purple: 'bg-purple-50 border-purple-200 text-purple-600',
      red: 'bg-red-50 border-red-200 text-red-600',
      orange: 'bg-orange-50 border-orange-200 text-orange-600',
      indigo: 'bg-indigo-50 border-indigo-200 text-indigo-600'
    };

    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-200">
        <div className="flex items-center justify-between">
          <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
            <Icon className="h-6 w-6" />
          </div>
          {trend && (
            <div className={`flex items-center text-sm ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
              {trend === 'up' ? <TrendingUp className="h-4 w-4 mr-1" /> : <TrendingDown className="h-4 w-4 mr-1" />}
              {trendValue}%
            </div>
          )}
        </div>
        <div className="mt-4">
          <h3 className="text-2xl font-bold text-gray-900">{value?.toLocaleString() || 0}</h3>
          <p className="text-gray-600 font-medium">{title}</p>
          <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
        </div>
      </div>
    );
  };

  const QuickActionCard = ({ title, description, icon: Icon, color, onClick }) => {
    const colorClasses = {
      blue: 'bg-blue-500 hover:bg-blue-600',
      green: 'bg-green-500 hover:bg-green-600',
      purple: 'bg-purple-500 hover:bg-purple-600',
      orange: 'bg-orange-500 hover:bg-orange-600'
    };

    return (
      <div 
        onClick={onClick}
        className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-200 cursor-pointer group"
      >
        <div className={`inline-flex p-3 rounded-lg text-white ${colorClasses[color]} group-hover:scale-110 transition-transform duration-200`}>
          <Icon className="h-6 w-6" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mt-4">{title}</h3>
        <p className="text-gray-600 text-sm mt-2">{description}</p>
      </div>
    );
  };

  const RecentActivityItem = ({ type, description, time, status }) => {
    const statusColors = {
      success: 'bg-green-100 text-green-800',
      warning: 'bg-yellow-100 text-yellow-800',
      error: 'bg-red-100 text-red-800',
      info: 'bg-blue-100 text-blue-800'
    };

    return (
      <div className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
        <div className="flex items-center space-x-3 space-x-reverse">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          <div>
            <p className="text-sm font-medium text-gray-900">{description}</p>
            <p className="text-xs text-gray-500">{time}</p>
          </div>
        </div>
        <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusColors[status]}`}>
          {type}
        </span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-gray-200 h-32 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
            <p className="text-red-700">{error}</p>
            <button 
              onClick={fetchDashboardData}
              className="mr-auto bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded text-sm"
            >
              إعادة المحاولة
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">لوحة التحكم</h1>
          <p className="text-gray-600 mt-1">نظرة عامة على حالة المخزون والعمليات</p>
        </div>
        <div className="flex items-center space-x-3 space-x-reverse">
          {lastUpdated && (
            <p className="text-sm text-gray-500">
              آخر تحديث: {lastUpdated.toLocaleTimeString('ar-SA')}
            </p>
          )}
          <button
            onClick={fetchDashboardData}
            className="flex items-center space-x-2 space-x-reverse bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"
          >
            <RefreshCw className="h-4 w-4" />
            <span>تحديث</span>
          </button>
        </div>
      </div>

      {/* KPI Cards */}
      {kpis && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <KPICard
            title="إجمالي المنتجات"
            value={kpis.inventory?.totalProducts || 0}
            icon={Package}
            color="blue"
            subtitle="منتج مسجل في النظام"
            trend="up"
            trendValue="12"
          />
          <KPICard
            title="إجمالي الموردين"
            value={kpis.suppliers?.totalSuppliers || 0}
            icon={Users}
            color="green"
            subtitle="مورد نشط"
            trend="up"
            trendValue="5"
          />
          <KPICard
            title="المستودعات"
            value={kpis.inventory?.totalWarehouses || 0}
            icon={Warehouse}
            color="purple"
            subtitle="مستودع متاح"
          />
          <KPICard
            title="المنتجات المنخفضة"
            value={kpis.inventory?.lowStockProducts || 0}
            icon={AlertTriangle}
            color="red"
            subtitle="منتج تحت الحد الأدنى"
            trend="down"
            trendValue="8"
          />
        </div>
      )}

      {/* Secondary KPIs */}
      {kpis && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <KPICard
            title="قيمة المخزون"
            value={kpis.inventory?.totalValue || 0}
            icon={DollarSign}
            color="indigo"
            subtitle="ريال سعودي"
            trend="up"
            trendValue="15"
          />
          <KPICard
            title="أوامر الشراء المعلقة"
            value={kpis.orders?.pendingPurchaseOrders || 0}
            icon={ShoppingCart}
            color="orange"
            subtitle="أمر في الانتظار"
          />
          <KPICard
            title="معدل دوران المخزون"
            value={kpis.inventory?.turnoverRate || 0}
            icon={Activity}
            color="green"
            subtitle="مرة في الشهر"
            trend="up"
            trendValue="3"
          />
        </div>
      )}

      {/* Quick Actions & Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Quick Actions */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">إجراءات سريعة</h2>
          <div className="grid grid-cols-2 gap-4">
            <QuickActionCard
              title="إضافة منتج"
              description="إضافة منتج جديد للمخزون"
              icon={Plus}
              color="blue"
              onClick={() => window.location.href = '/products'}
            />
            <QuickActionCard
              title="عرض التقارير"
              description="مراجعة تقارير المخزون"
              icon={Eye}
              color="green"
              onClick={() => window.location.href = '/reports'}
            />
            <QuickActionCard
              title="إدارة الموردين"
              description="إضافة أو تعديل الموردين"
              icon={Users}
              color="purple"
              onClick={() => window.location.href = '/suppliers'}
            />
            <QuickActionCard
              title="حركة المخزون"
              description="تتبع حركة المنتجات"
              icon={Activity}
              color="orange"
              onClick={() => window.location.href = '/movements'}
            />
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">النشاط الأخير</h2>
          <div className="space-y-1">
            <RecentActivityItem
              type="إضافة"
              description="تم إضافة منتج جديد: لابتوب Dell"
              time="منذ 5 دقائق"
              status="success"
            />
            <RecentActivityItem
              type="تنبيه"
              description="مخزون منتج iPhone 15 منخفض"
              time="منذ 15 دقيقة"
              status="warning"
            />
            <RecentActivityItem
              type="تحديث"
              description="تم تحديث معلومات المورد ABC Company"
              time="منذ 30 دقيقة"
              status="info"
            />
            <RecentActivityItem
              type="حذف"
              description="تم حذف منتج منتهي الصلاحية"
              time="منذ ساعة"
              status="error"
            />
            <RecentActivityItem
              type="إضافة"
              description="تم إنشاء أمر شراء جديد #1234"
              time="منذ ساعتين"
              status="success"
            />
          </div>
          <div className="mt-4 pt-4 border-t border-gray-100">
            <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
              عرض جميع الأنشطة ←
            </button>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Sales Chart Placeholder */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">مبيعات الشهر</h2>
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">الرسم البياني قيد التطوير</p>
            </div>
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">أفضل المنتجات مبيعاً</h2>
          <div className="space-y-4">
            {[
              { name: 'iPhone 15 Pro', sales: 45, percentage: 85 },
              { name: 'Samsung Galaxy S24', sales: 32, percentage: 65 },
              { name: 'MacBook Air M3', sales: 28, percentage: 55 },
              { name: 'iPad Pro', sales: 22, percentage: 45 },
              { name: 'AirPods Pro', sales: 18, percentage: 35 }
            ].map((product, index) => (
              <div key={index} className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">{product.name}</p>
                  <p className="text-sm text-gray-500">{product.sales} مبيعة</p>
                </div>
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full" 
                      style={{ width: `${product.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-600">{product.percentage}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

