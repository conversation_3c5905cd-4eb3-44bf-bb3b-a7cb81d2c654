import { useState, useEffect } from 'react';
import { Package } from 'lucide-react';
import { dashboardService } from '../services/dashboardService';

const Dashboard = () => {
  const [kpis, setKpis] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const data = await dashboardService.getDashboard();
      setKpis(data.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const KPICard = ({ title, value, trend, trendValue }) => {
    return (
      <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#40474f]">
        <p className="text-white text-base font-medium leading-normal">{title}</p>
        <p className="text-white tracking-light text-2xl font-bold leading-tight">{value?.toLocaleString() || 0}</p>
        {trend && (
          <p className={`text-base font-medium leading-normal ${trend === 'up' ? 'text-[#0bda5b]' : 'text-[#fa6238]'}`}>
            {trend === 'up' ? '+' : ''}{trendValue}%
          </p>
        )}
      </div>
    );
  };



  if (loading) {
    return (
      <div className="text-white p-4">
        <h1 className="text-2xl">Loading...</h1>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-white p-4">
        <h1 className="text-2xl text-red-400">Error: {error}</h1>
        <button
          onClick={fetchDashboardData}
          className="mt-4 bg-blue-500 text-white px-4 py-2 rounded"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <div className="flex flex-wrap justify-between gap-3 p-4">
        <p className="text-white tracking-light text-[32px] font-bold leading-tight min-w-72">Dashboard</p>
      </div>
      <p className="text-white text-base font-normal leading-normal pb-3 pt-1 px-4">Welcome back, Alex! Here's a snapshot of your warehouse operations.</p>

      {/* KPI Cards */}
      {kpis && (
        <div className="flex flex-wrap gap-4 p-4">
          <KPICard
            title="Total Products"
            value={kpis.inventory?.totalProducts || 1250}
            trend="up"
            trendValue="5"
          />
          <KPICard
            title="Stock Value"
            value={`$${(kpis.inventory?.totalValue || 500000).toLocaleString()}`}
            trend="down"
            trendValue="2"
          />
          <KPICard
            title="Orders Pending"
            value={kpis.orders?.pendingPurchaseOrders || 25}
            trend="up"
            trendValue="10"
          />
          <KPICard
            title="Slow-moving Items"
            value={kpis.inventory?.lowStockProducts || 100}
            trend="down"
            trendValue="3"
          />
        </div>
      )}

      {/* Stock Distribution */}
      <h2 className="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Stock Distribution</h2>
      <div className="flex flex-wrap gap-4 px-4 py-6">
        <div className="flex min-w-72 flex-1 flex-col gap-2 rounded-xl border border-[#40474f] p-6">
          <p className="text-white text-base font-medium leading-normal">Stock Distribution by Warehouse</p>
          <p className="text-white tracking-light text-[32px] font-bold leading-tight truncate">10000</p>
          <div className="flex gap-1">
            <p className="text-[#a2abb3] text-base font-normal leading-normal">Current</p>
            <p className="text-[#0bda5b] text-base font-medium leading-normal">+5%</p>
          </div>
          <div className="grid min-h-[180px] grid-flow-col gap-6 grid-rows-[1fr_auto] items-end justify-items-center px-3">
            <div className="border-[#a2abb3] bg-[#2c3035] border-t-2 w-full" style={{height: '50%'}}></div>
            <p className="text-[#a2abb3] text-[13px] font-bold leading-normal tracking-[0.015em]">Warehouse A</p>
            <div className="border-[#a2abb3] bg-[#2c3035] border-t-2 w-full" style={{height: '60%'}}></div>
            <p className="text-[#a2abb3] text-[13px] font-bold leading-normal tracking-[0.015em]">Warehouse B</p>
            <div className="border-[#a2abb3] bg-[#2c3035] border-t-2 w-full" style={{height: '70%'}}></div>
            <p className="text-[#a2abb3] text-[13px] font-bold leading-normal tracking-[0.015em]">Warehouse C</p>
          </div>
        </div>
      </div>

      {/* Low Stock Alerts */}
      <h2 className="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Low Stock Alerts</h2>
      <div className="flex items-center gap-4 bg-[#121416] px-4 min-h-[72px] py-2">
        <div className="text-white flex items-center justify-center rounded-lg bg-[#2c3035] shrink-0 size-12">
          <Package size={24} />
        </div>
        <div className="flex flex-col justify-center">
          <p className="text-white text-base font-medium leading-normal line-clamp-1">Product X - 5 units</p>
          <p className="text-[#a2abb3] text-sm font-normal leading-normal line-clamp-2">Warehouse A</p>
        </div>
      </div>
      <div className="flex items-center gap-4 bg-[#121416] px-4 min-h-[72px] py-2">
        <div className="text-white flex items-center justify-center rounded-lg bg-[#2c3035] shrink-0 size-12">
          <Package size={24} />
        </div>
        <div className="flex flex-col justify-center">
          <p className="text-white text-base font-medium leading-normal line-clamp-1">Product Y - 2 units</p>
          <p className="text-[#a2abb3] text-sm font-normal leading-normal line-clamp-2">Warehouse B</p>
        </div>
      </div>
      <div className="flex items-center gap-4 bg-[#121416] px-4 min-h-[72px] py-2">
        <div className="text-white flex items-center justify-center rounded-lg bg-[#2c3035] shrink-0 size-12">
          <Package size={24} />
        </div>
        <div className="flex flex-col justify-center">
          <p className="text-white text-base font-medium leading-normal line-clamp-1">Product Z - 1 unit</p>
          <p className="text-[#a2abb3] text-sm font-normal leading-normal line-clamp-2">Warehouse C</p>
        </div>
      </div>

    </>
  );
};

export default Dashboard;

