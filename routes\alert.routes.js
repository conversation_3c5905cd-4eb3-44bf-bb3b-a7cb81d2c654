const express = require('express');
const router = express.Router();
const alertController = require('../controllers/alert.controller');

// Middleware for request logging
const requestLogger = (req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.originalUrl}`);
  next();
};

// Apply logging middleware to all routes
router.use(requestLogger);

// Validation middleware for ID parameter
const validateIdParam = (req, res, next) => {
  const { id } = req.params;
  
  if (!id || isNaN(parseInt(id))) {
    return res.status(400).json({
      success: false,
      message: 'معرف التنبيه يجب أن يكون رقم صحيح',
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

// Validation middleware for query parameters
const validateQueryParams = (req, res, next) => {
  const { page, limit, level, status, type } = req.query;
  
  // Validate page parameter
  if (page && (isNaN(parseInt(page)) || parseInt(page) < 1)) {
    return res.status(400).json({
      success: false,
      message: 'رقم الصفحة يجب أن يكون رقم أكبر من 0',
      timestamp: new Date().toISOString()
    });
  }
  
  // Validate limit parameter
  if (limit && (isNaN(parseInt(limit)) || parseInt(limit) < 1 || parseInt(limit) > 100)) {
    return res.status(400).json({
      success: false,
      message: 'الحد الأقصى يجب أن يكون رقم بين 1 و 100',
      timestamp: new Date().toISOString()
    });
  }
  
  // Validate level parameter
  if (level && !['CRITICAL', 'HIGH', 'MEDIUM'].includes(level)) {
    return res.status(400).json({
      success: false,
      message: 'مستوى التنبيه يجب أن يكون CRITICAL أو HIGH أو MEDIUM',
      timestamp: new Date().toISOString()
    });
  }
  
  // Validate status parameter
  if (status && !['NEW', 'SEEN', 'RESOLVED'].includes(status)) {
    return res.status(400).json({
      success: false,
      message: 'حالة التنبيه يجب أن تكون NEW أو SEEN أو RESOLVED',
      timestamp: new Date().toISOString()
    });
  }
  
  // Validate type parameter
  if (type && !['LOW_STOCK', 'OUT_OF_STOCK'].includes(type)) {
    return res.status(400).json({
      success: false,
      message: 'نوع التنبيه يجب أن يكون LOW_STOCK أو OUT_OF_STOCK',
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

// Validation middleware for bulk operations
const validateBulkData = (req, res, next) => {
  const { alert_ids } = req.body;
  
  if (!alert_ids || !Array.isArray(alert_ids) || alert_ids.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'معرفات التنبيهات مطلوبة ويجب أن تكون مصفوفة غير فارغة',
      timestamp: new Date().toISOString()
    });
  }
  
  // Validate each ID
  for (let i = 0; i < alert_ids.length; i++) {
    if (isNaN(parseInt(alert_ids[i]))) {
      return res.status(400).json({
        success: false,
        message: `معرف التنبيه في الموضع ${i + 1} يجب أن يكون رقم صحيح`,
        timestamp: new Date().toISOString()
      });
    }
  }
  
  next();
};

// Routes

/**
 * @route   GET /api/alerts
 * @desc    Get all alerts with optional filtering and pagination
 * @access  Public
 * @params  ?page=1&limit=10&level=CRITICAL&status=NEW&type=LOW_STOCK
 */
router.get('/', validateQueryParams, alertController.getAllAlerts);

/**
 * @route   GET /api/alerts/active
 * @desc    Get only active alerts (NEW and SEEN status)
 * @access  Public
 * @returns {Object} Active alerts grouped by level
 */
router.get('/active', alertController.getActiveAlerts);

/**
 * @route   GET /api/alerts/stats
 * @desc    Get alert statistics
 * @access  Public
 * @returns {Object} Alert statistics by level, status, and trends
 */
router.get('/stats', alertController.getAlertStats);

/**
 * @route   POST /api/alerts/check-all
 * @desc    Check all inventory items and create/resolve alerts as needed
 * @access  Public
 * @returns {Object} Summary of alerts created and resolved
 */
router.post('/check-all', alertController.checkAllInventoryAlerts);

/**
 * @route   POST /api/alerts/bulk-resolve
 * @desc    Resolve multiple alerts at once
 * @access  Public
 * @body    { alert_ids: [1, 2, 3], resolution_note?: "string" }
 */
router.post('/bulk-resolve', validateBulkData, alertController.bulkResolveAlerts);

/**
 * @route   GET /api/alerts/:id
 * @desc    Get single alert by ID (automatically marks as SEEN)
 * @access  Public
 */
router.get('/:id', validateIdParam, alertController.getAlertById);

/**
 * @route   PUT /api/alerts/:id/seen
 * @desc    Mark alert as seen
 * @access  Public
 */
router.put('/:id/seen', validateIdParam, alertController.markAsSeen);

/**
 * @route   PUT /api/alerts/:id/resolve
 * @desc    Resolve alert
 * @access  Public
 * @body    { resolution_note?: "string" }
 */
router.put('/:id/resolve', validateIdParam, alertController.resolveAlert);

module.exports = router;
