const { SalesOrder, SalesOrderItem, Product, Inventory, StockMovement, sequelize } = require('../models');
const { Op } = require('sequelize');

// Helper function for error response
const sendErrorResponse = (res, statusCode, message, error = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (error && process.env.NODE_ENV === 'development') {
    response.error = error.message;
  }
  
  return res.status(statusCode).json(response);
};

// Helper function for success response
const sendSuccessResponse = (res, statusCode, message, data = null) => {
  const response = {
    success: true,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (data !== null) {
    response.data = data;
  }
  
  return res.status(statusCode).json(response);
};

// Get all sales orders
exports.getAllSalesOrders = async (req, res) => {
  try {
    const { page = 1, limit = 10, customer_name, status, start_date, end_date } = req.query;
    const offset = (page - 1) * limit;
    
    // Build where clause
    const whereClause = {};
    
    if (customer_name) {
      whereClause.customer_name = {
        [Op.iLike]: `%${customer_name}%`
      };
    }
    
    if (status) {
      whereClause.status = status;
    }
    
    if (start_date && end_date) {
      whereClause.order_date = {
        [Op.between]: [new Date(start_date), new Date(end_date)]
      };
    } else if (start_date) {
      whereClause.order_date = {
        [Op.gte]: new Date(start_date)
      };
    } else if (end_date) {
      whereClause.order_date = {
        [Op.lte]: new Date(end_date)
      };
    }
    
    const { count, rows } = await SalesOrder.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: SalesOrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'name', 'code', 'unit']
            }
          ]
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['order_date', 'DESC']]
    });
    
    const totalPages = Math.ceil(count / limit);
    
    sendSuccessResponse(res, 200, 'تم جلب أوامر البيع بنجاح', {
      salesOrders: rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error in getAllSalesOrders:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب أوامر البيع', error);
  }
};

// Get single sales order by ID
exports.getSalesOrderById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const salesOrder = await SalesOrder.findByPk(id, {
      include: [
        {
          model: SalesOrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product'
            }
          ]
        }
      ]
    });
    
    if (!salesOrder) {
      return sendErrorResponse(res, 404, 'أمر البيع غير موجود');
    }
    
    // Calculate completion percentage
    const completionPercentage = await salesOrder.getCompletionPercentage();
    
    const response = {
      ...salesOrder.toJSON(),
      completion_percentage: completionPercentage
    };
    
    sendSuccessResponse(res, 200, 'تم جلب أمر البيع بنجاح', { salesOrder: response });
  } catch (error) {
    console.error('Error in getSalesOrderById:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب أمر البيع', error);
  }
};

// Create new sales order
exports.createSalesOrder = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { customer_name, customer_phone, delivery_date, notes, items } = req.body;
    
    // Validation
    if (!customer_name || !items || !Array.isArray(items) || items.length === 0) {
      await transaction.rollback();
      return sendErrorResponse(res, 400, 'اسم العميل وعناصر الطلب مطلوبة');
    }
    
    // Generate order number
    const orderNumber = await SalesOrder.generateOrderNumber();
    
    // Create sales order
    const salesOrder = await SalesOrder.create({
      order_number: orderNumber,
      customer_name,
      customer_phone,
      delivery_date: delivery_date ? new Date(delivery_date) : null,
      notes
    }, { transaction });
    
    // Validate and create items
    let totalAmount = 0;
    const createdItems = [];
    
    for (const item of items) {
      const { product_id, quantity_ordered, unit_price } = item;
      
      if (!product_id || !quantity_ordered || quantity_ordered < 1) {
        await transaction.rollback();
        return sendErrorResponse(res, 400, 'معرف المنتج والكمية المطلوبة مطلوبان لكل عنصر');
      }
      
      // Check if product exists
      const product = await Product.findByPk(product_id);
      if (!product) {
        await transaction.rollback();
        return sendErrorResponse(res, 404, `المنتج بالمعرف ${product_id} غير موجود`);
      }
      
      const orderItem = await SalesOrderItem.create({
        sales_order_id: salesOrder.id,
        product_id,
        quantity_ordered,
        unit_price: unit_price || null
      }, { transaction });
      
      createdItems.push(orderItem);
      
      if (unit_price) {
        totalAmount += quantity_ordered * unit_price;
      }
    }
    
    // Update total amount
    await salesOrder.update({
      total_amount: totalAmount > 0 ? totalAmount : null
    }, { transaction });
    
    await transaction.commit();
    
    // Fetch the created sales order with associations
    const createdSalesOrder = await SalesOrder.findByPk(salesOrder.id, {
      include: [
        {
          model: SalesOrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product'
            }
          ]
        }
      ]
    });
    
    sendSuccessResponse(res, 201, 'تم إنشاء أمر البيع بنجاح', { salesOrder: createdSalesOrder });
  } catch (error) {
    await transaction.rollback();
    console.error('Error in createSalesOrder:', error);
    
    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => err.message);
      return sendErrorResponse(res, 400, validationErrors.join(', '));
    }
    
    sendErrorResponse(res, 500, 'خطأ في إنشاء أمر البيع', error);
  }
};

// Update sales order
exports.updateSalesOrder = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { customer_name, customer_phone, status, delivery_date, notes, total_amount } = req.body;

    const salesOrder = await SalesOrder.findByPk(id, { transaction });

    if (!salesOrder) {
      await transaction.rollback();
      return sendErrorResponse(res, 404, 'أمر البيع غير موجود');
    }

    // Update sales order
    await salesOrder.update({
      customer_name: customer_name || salesOrder.customer_name,
      customer_phone: customer_phone !== undefined ? customer_phone : salesOrder.customer_phone,
      status: status || salesOrder.status,
      delivery_date: delivery_date ? new Date(delivery_date) : salesOrder.delivery_date,
      notes: notes !== undefined ? notes : salesOrder.notes,
      total_amount: total_amount !== undefined ? total_amount : salesOrder.total_amount
    }, { transaction });

    await transaction.commit();

    // Fetch updated sales order with associations
    const updatedSalesOrder = await SalesOrder.findByPk(id, {
      include: [
        {
          model: SalesOrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product'
            }
          ]
        }
      ]
    });

    sendSuccessResponse(res, 200, 'تم تحديث أمر البيع بنجاح', { salesOrder: updatedSalesOrder });
  } catch (error) {
    await transaction.rollback();
    console.error('Error in updateSalesOrder:', error);

    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => err.message);
      return sendErrorResponse(res, 400, validationErrors.join(', '));
    }

    sendErrorResponse(res, 500, 'خطأ في تحديث أمر البيع', error);
  }
};

// Delete sales order
exports.deleteSalesOrder = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    const salesOrder = await SalesOrder.findByPk(id, { transaction });

    if (!salesOrder) {
      await transaction.rollback();
      return sendErrorResponse(res, 404, 'أمر البيع غير موجود');
    }

    // Check if order can be deleted (only NEW orders can be deleted)
    if (salesOrder.status !== 'NEW') {
      await transaction.rollback();
      return sendErrorResponse(res, 400, 'لا يمكن حذف أمر البيع إلا إذا كان في حالة جديد');
    }

    await salesOrder.destroy({ transaction });
    await transaction.commit();

    sendSuccessResponse(res, 200, 'تم حذف أمر البيع بنجاح');
  } catch (error) {
    await transaction.rollback();
    console.error('Error in deleteSalesOrder:', error);
    sendErrorResponse(res, 500, 'خطأ في حذف أمر البيع', error);
  }
};

// Deliver products for sales order
exports.deliverProducts = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { items, warehouse_id = 1 } = req.body; // Default to warehouse 1 if not specified

    if (!items || !Array.isArray(items) || items.length === 0) {
      await transaction.rollback();
      return sendErrorResponse(res, 400, 'عناصر التسليم مطلوبة');
    }

    const salesOrder = await SalesOrder.findByPk(id, {
      include: [
        {
          model: SalesOrderItem,
          as: 'items'
        }
      ],
      transaction
    });

    if (!salesOrder) {
      await transaction.rollback();
      return sendErrorResponse(res, 404, 'أمر البيع غير موجود');
    }

    if (salesOrder.status === 'COMPLETED' || salesOrder.status === 'CANCELLED') {
      await transaction.rollback();
      return sendErrorResponse(res, 400, 'لا يمكن تسليم منتجات لأمر بيع مكتمل أو ملغي');
    }

    const deliveredItems = [];

    for (const deliveredItem of items) {
      const { product_id, quantity_delivered } = deliveredItem;

      if (!product_id || !quantity_delivered || quantity_delivered < 1) {
        await transaction.rollback();
        return sendErrorResponse(res, 400, 'معرف المنتج والكمية المسلمة مطلوبان');
      }

      // Find the sales order item
      const orderItem = await SalesOrderItem.findOne({
        where: {
          sales_order_id: id,
          product_id: product_id
        },
        transaction
      });

      if (!orderItem) {
        await transaction.rollback();
        return sendErrorResponse(res, 404, `المنتج بالمعرف ${product_id} غير موجود في أمر البيع`);
      }

      // Check if quantity doesn't exceed ordered quantity
      const newTotalDelivered = orderItem.quantity_delivered + quantity_delivered;
      if (newTotalDelivered > orderItem.quantity_ordered) {
        await transaction.rollback();
        return sendErrorResponse(res, 400, `الكمية المسلمة (${newTotalDelivered}) تتجاوز الكمية المطلوبة (${orderItem.quantity_ordered}) للمنتج ${product_id}`);
      }

      // Check inventory availability
      const inventory = await Inventory.findOne({
        where: {
          product_id: product_id,
          warehouse_id: warehouse_id
        },
        transaction
      });

      if (!inventory || inventory.quantity < quantity_delivered) {
        await transaction.rollback();
        return sendErrorResponse(res, 400, `الكمية المتاحة في المخزون (${inventory ? inventory.quantity : 0}) أقل من الكمية المطلوبة للتسليم (${quantity_delivered}) للمنتج ${product_id}`);
      }

      // Update sales order item
      await orderItem.update({
        quantity_delivered: newTotalDelivered
      }, { transaction });

      // Create stock movement (OUT)
      await StockMovement.create({
        product_id: product_id,
        warehouse_id: warehouse_id,
        type: 'OUT',
        quantity: quantity_delivered,
        reference: salesOrder.order_number,
        description: `تسليم من أمر البيع ${salesOrder.order_number} للعميل ${salesOrder.customer_name}`
      }, { transaction });

      // Update inventory
      await inventory.update({
        quantity: inventory.quantity - quantity_delivered,
        last_checked_at: new Date()
      }, { transaction });

      deliveredItems.push({
        product_id,
        quantity_delivered,
        total_delivered: newTotalDelivered,
        quantity_ordered: orderItem.quantity_ordered
      });
    }

    // Update sales order status
    const allItems = await SalesOrderItem.findAll({
      where: { sales_order_id: id },
      transaction
    });

    const allFullyDelivered = allItems.every(item => item.quantity_delivered >= item.quantity_ordered);
    const anyPartiallyDelivered = allItems.some(item => item.quantity_delivered > 0);

    let newStatus = salesOrder.status;
    if (allFullyDelivered) {
      newStatus = 'COMPLETED';
    } else if (anyPartiallyDelivered) {
      newStatus = 'PARTIALLY_DELIVERED';
    }

    await salesOrder.update({ status: newStatus }, { transaction });

    await transaction.commit();

    sendSuccessResponse(res, 200, 'تم تسليم المنتجات بنجاح', {
      deliveredItems,
      newStatus,
      message: `تم تسليم ${deliveredItems.length} منتج وتحديث المخزون`
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error in deliverProducts:', error);
    sendErrorResponse(res, 500, 'خطأ في تسليم المنتجات', error);
  }
};

// Get sales orders statistics
exports.getSalesOrdersStats = async (req, res) => {
  try {
    const totalOrders = await SalesOrder.count();
    const newOrders = await SalesOrder.count({
      where: { status: 'NEW' }
    });
    const partiallyDeliveredOrders = await SalesOrder.count({
      where: { status: 'PARTIALLY_DELIVERED' }
    });
    const completedOrders = await SalesOrder.count({
      where: { status: 'COMPLETED' }
    });
    const cancelledOrders = await SalesOrder.count({
      where: { status: 'CANCELLED' }
    });

    const totalRevenue = await SalesOrder.sum('total_amount', {
      where: {
        total_amount: { [Op.ne]: null },
        status: { [Op.ne]: 'CANCELLED' }
      }
    }) || 0;

    // Orders by customer
    const ordersByCustomer = await SalesOrder.findAll({
      attributes: [
        'customer_name',
        [SalesOrder.sequelize.fn('COUNT', SalesOrder.sequelize.col('id')), 'order_count'],
        [SalesOrder.sequelize.fn('SUM', SalesOrder.sequelize.col('total_amount')), 'total_value']
      ],
      where: {
        total_amount: { [Op.ne]: null }
      },
      group: ['customer_name'],
      order: [[SalesOrder.sequelize.fn('COUNT', SalesOrder.sequelize.col('id')), 'DESC']],
      limit: 10
    });

    // Recent orders
    const recentOrders = await SalesOrder.findAll({
      order: [['order_date', 'DESC']],
      limit: 5
    });

    // Top selling products
    const topSellingProducts = await SalesOrderItem.getTopSellingProducts(10);

    // Pending items (not fully delivered)
    const pendingItems = await SalesOrderItem.findAll({
      where: {
        quantity_delivered: {
          [Op.lt]: SalesOrder.sequelize.col('quantity_ordered')
        }
      },
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['name', 'code']
        },
        {
          model: SalesOrder,
          as: 'salesOrder',
          attributes: ['order_number', 'customer_name', 'status']
        }
      ],
      limit: 10
    });

    // Monthly sales trend (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlySales = await SalesOrder.findAll({
      attributes: [
        [SalesOrder.sequelize.fn('DATE_TRUNC', 'month', SalesOrder.sequelize.col('order_date')), 'month'],
        [SalesOrder.sequelize.fn('COUNT', SalesOrder.sequelize.col('id')), 'order_count'],
        [SalesOrder.sequelize.fn('SUM', SalesOrder.sequelize.col('total_amount')), 'total_revenue']
      ],
      where: {
        order_date: {
          [Op.gte]: sixMonthsAgo
        },
        total_amount: { [Op.ne]: null }
      },
      group: [SalesOrder.sequelize.fn('DATE_TRUNC', 'month', SalesOrder.sequelize.col('order_date'))],
      order: [[SalesOrder.sequelize.fn('DATE_TRUNC', 'month', SalesOrder.sequelize.col('order_date')), 'ASC']]
    });

    const stats = {
      totalOrders,
      ordersByStatus: {
        new: newOrders,
        partiallyDelivered: partiallyDeliveredOrders,
        completed: completedOrders,
        cancelled: cancelledOrders
      },
      totalRevenue,
      ordersByCustomer,
      recentOrders,
      topSellingProducts,
      pendingItems,
      monthlySales
    };

    sendSuccessResponse(res, 200, 'تم جلب إحصائيات أوامر البيع بنجاح', { stats });
  } catch (error) {
    console.error('Error in getSalesOrdersStats:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب إحصائيات أوامر البيع', error);
  }
};
