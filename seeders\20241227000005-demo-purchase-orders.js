'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Get actual supplier IDs from database
    const suppliers = await queryInterface.sequelize.query(
      'SELECT id FROM suppliers ORDER BY id LIMIT 3',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );
    
    if (suppliers.length > 0) {
      await queryInterface.bulkInsert('purchase_orders', [
        {
          supplier_id: suppliers[0].id,
          order_number: 'PO-2024-001',
          status: 'NEW',
          total_amount: 15000.00,
          order_date: new Date('2024-12-20T09:00:00'),
          delivery_date: new Date('2024-12-30T09:00:00'),
          notes: 'طلب شراء للمنتجات التقنية الأساسية',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          supplier_id: suppliers[1].id,
          order_number: 'PO-2024-002',
          status: 'PARTIALLY_DELIVERED',
          total_amount: 8500.00,
          order_date: new Date('2024-12-22T10:30:00'),
          delivery_date: new Date('2025-01-05T10:00:00'),
          notes: 'طلب شراء للملحقات والأجهزة المكتبية',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          supplier_id: suppliers[2].id,
          order_number: 'PO-2024-003',
          status: 'COMPLETED',
          total_amount: 12000.00,
          order_date: new Date('2024-12-18T14:15:00'),
          delivery_date: new Date('2024-12-25T14:00:00'),
          notes: 'طلب شراء للمعدات الإلكترونية',
          created_at: new Date(),
          updated_at: new Date()
        }
      ], {});
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('purchase_orders', null, {});
  }
};
