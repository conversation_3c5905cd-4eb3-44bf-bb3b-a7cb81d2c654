'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Inventory extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Define associations here
      Inventory.belongsTo(models.Product, {
        foreignKey: 'product_id',
        as: 'product'
      });
      
      Inventory.belongsTo(models.Warehouse, {
        foreignKey: 'warehouse_id',
        as: 'warehouse'
      });
    }

    // Instance method to check if stock is low
    isLowStock() {
      if (!this.minimum_quantity) return false;
      return this.quantity <= this.minimum_quantity;
    }

    // Instance method to get stock status
    getStockStatus() {
      if (this.quantity === 0) return 'out_of_stock';
      if (this.isLowStock()) return 'low_stock';
      return 'in_stock';
    }

    // Instance method to get inventory summary
    getInventorySummary() {
      return {
        id: this.id,
        product_id: this.product_id,
        warehouse_id: this.warehouse_id,
        quantity: this.quantity,
        minimum_quantity: this.minimum_quantity,
        stock_status: this.getStockStatus(),
        last_checked_at: this.last_checked_at
      };
    }

    // Static method to find inventory by product
    static async findByProduct(productId) {
      return await this.findAll({
        where: {
          product_id: productId
        },
        include: [
          {
            model: sequelize.models.Warehouse,
            as: 'warehouse'
          }
        ]
      });
    }

    // Static method to find inventory by warehouse
    static async findByWarehouse(warehouseId) {
      return await this.findAll({
        where: {
          warehouse_id: warehouseId
        },
        include: [
          {
            model: sequelize.models.Product,
            as: 'product'
          }
        ]
      });
    }

    // Static method to find low stock items
    static async findLowStock() {
      return await this.findAll({
        where: {
          [sequelize.Sequelize.Op.and]: [
            sequelize.literal('"Inventory"."quantity" <= "Inventory"."minimum_quantity"'),
            { minimum_quantity: { [sequelize.Sequelize.Op.ne]: null } }
          ]
        },
        include: [
          {
            model: sequelize.models.Product,
            as: 'product'
          },
          {
            model: sequelize.models.Warehouse,
            as: 'warehouse'
          }
        ]
      });
    }

    // Static method to get total quantity for a product across all warehouses
    static async getTotalQuantityForProduct(productId) {
      const result = await this.sum('quantity', {
        where: {
          product_id: productId
        }
      });
      return result || 0;
    }
  }

  Inventory.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'products',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'معرف المنتج مطلوب'
        },
        isInt: {
          msg: 'معرف المنتج يجب أن يكون رقم صحيح'
        }
      }
    },
    warehouse_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'warehouses',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'معرف المستودع مطلوب'
        },
        isInt: {
          msg: 'معرف المستودع يجب أن يكون رقم صحيح'
        }
      }
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: {
          args: [0],
          msg: 'الكمية يجب أن تكون أكبر من أو تساوي 0'
        },
        isInt: {
          msg: 'الكمية يجب أن تكون رقم صحيح'
        }
      }
    },
    minimum_quantity: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: {
          args: [0],
          msg: 'الحد الأدنى للكمية يجب أن يكون أكبر من أو يساوي 0'
        },
        isInt: {
          msg: 'الحد الأدنى للكمية يجب أن يكون رقم صحيح'
        }
      }
    },
    last_checked_at: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'Inventory',
    tableName: 'inventory',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['product_id', 'warehouse_id'],
        name: 'inventory_product_warehouse_unique'
      },
      {
        fields: ['product_id']
      },
      {
        fields: ['warehouse_id']
      },
      {
        fields: ['quantity']
      },
      {
        fields: ['minimum_quantity']
      }
    ]
  });

  return Inventory;
};
