'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Get actual product and warehouse IDs from database
    const products = await queryInterface.sequelize.query(
      'SELECT id FROM products ORDER BY id LIMIT 3',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    const warehouses = await queryInterface.sequelize.query(
      'SELECT id FROM warehouses ORDER BY id LIMIT 2',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    if (products.length > 0 && warehouses.length > 0) {
      await queryInterface.bulkInsert('stock_movements', [
        // IN movements (إدخال)
        {
          product_id: products[0].id,
          warehouse_id: warehouses[0].id,
          type: 'IN',
          quantity: 25,
          reference: 'PO-2024-001',
          description: 'استلام شحنة جديدة من المورد',
          moved_at: new Date('2024-12-20T09:00:00'),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          product_id: products[1].id,
          warehouse_id: warehouses[0].id,
          type: 'IN',
          quantity: 30,
          reference: 'PO-2024-002',
          description: 'إضافة مخزون من المورد الرئيسي',
          moved_at: new Date('2024-12-21T10:30:00'),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          product_id: products[2].id,
          warehouse_id: warehouses[1].id,
          type: 'IN',
          quantity: 15,
          reference: 'PO-2024-003',
          description: 'تحويل مخزون من مستودع آخر',
          moved_at: new Date('2024-12-22T14:15:00'),
          created_at: new Date(),
          updated_at: new Date()
        },

        // OUT movements (إخراج)
        {
          product_id: products[0].id,
          warehouse_id: warehouses[0].id,
          type: 'OUT',
          quantity: 5,
          reference: 'SO-2024-001',
          description: 'بيع للعميل - فاتورة رقم 1001',
          moved_at: new Date('2024-12-23T11:45:00'),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          product_id: products[1].id,
          warehouse_id: warehouses[0].id,
          type: 'OUT',
          quantity: 8,
          reference: 'SO-2024-002',
          description: 'شحن طلبية للعميل الكبير',
          moved_at: new Date('2024-12-24T16:20:00'),
          created_at: new Date(),
          updated_at: new Date()
        }
      ], {});
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('stock_movements', null, {});
  }
};
