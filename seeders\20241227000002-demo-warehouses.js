'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkInsert('warehouses', [
      {
        name: 'المستودع الرئيسي - الرياض',
        code: 'WH-RYD-001',
        location: 'الرياض، حي الملك فهد، شارع الملك عبدالعزيز',
        capacity: 10000,
        description: 'المستودع الرئيسي في الرياض، يحتوي على جميع أنواع المنتجات التقنية والإلكترونية',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'مستودع جدة التجاري',
        code: 'WH-JED-001',
        location: 'جدة، حي الروضة، طريق الملك عبدالله',
        capacity: 7500,
        description: 'مستودع متخصص في المنتجات التجارية والمكتبية في منطقة جدة',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'مستودع الدمام الصناعي',
        code: 'WH-DMM-001',
        location: 'الدمام، المنطقة الصناعية الثانية، شارع الأمير محمد بن فهد',
        capacity: 12000,
        description: 'مستودع كبير متخصص في المعدات الصناعية والتقنية المتقدمة',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'مستودع مكة المكرمة',
        code: 'WH-MKK-001',
        location: 'مكة المكرمة، حي العزيزية، طريق الحرم',
        capacity: 5000,
        description: 'مستودع متوسط الحجم يخدم منطقة مكة المكرمة والمناطق المجاورة',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'مستودع المدينة المنورة',
        code: 'WH-MED-001',
        location: 'المدينة المنورة، حي السلام، شارع قباء',
        capacity: 3000,
        description: 'مستودع صغير متخصص في المنتجات الاستهلاكية والمكتبية',
        created_at: new Date(),
        updated_at: new Date()
      }
    ], {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('warehouses', null, {});
  }
};
