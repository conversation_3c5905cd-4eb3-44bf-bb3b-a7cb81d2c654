{"info": {"name": "Sales Orders Management API", "description": "مجموعة أمثلة لاختبار API إدارة أوامر البيع", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Sales Orders", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/sales-orders", "host": ["{{baseUrl}}"], "path": ["api", "sales-orders"]}}}, {"name": "Get Sales Orders with Pagination", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/sales-orders?page=1&limit=5", "host": ["{{baseUrl}}"], "path": ["api", "sales-orders"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "5"}]}}}, {"name": "Get Sales Orders by Customer", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/sales-orders?customer_name=أحمد", "host": ["{{baseUrl}}"], "path": ["api", "sales-orders"], "query": [{"key": "customer_name", "value": "<PERSON><PERSON><PERSON><PERSON>"}]}}}, {"name": "Get Sales Orders by Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/sales-orders?status=NEW", "host": ["{{baseUrl}}"], "path": ["api", "sales-orders"], "query": [{"key": "status", "value": "NEW"}]}}}, {"name": "Get Sales Orders by Date Range", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/sales-orders?start_date=2024-12-01&end_date=2024-12-31", "host": ["{{baseUrl}}"], "path": ["api", "sales-orders"], "query": [{"key": "start_date", "value": "2024-12-01"}, {"key": "end_date", "value": "2024-12-31"}]}}}, {"name": "Get Sales Order by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/sales-orders/1", "host": ["{{baseUrl}}"], "path": ["api", "sales-orders", "1"]}}}, {"name": "Create Sales Order - Basic", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_name\": \"سارة عبدالرحمن القحطاني\",\n  \"customer_phone\": \"+966505678901\",\n  \"delivery_date\": \"2025-01-10T14:00:00.000Z\",\n  \"notes\": \"طلب للمكتب الجديد - عاجل\",\n  \"items\": [\n    {\n      \"product_id\": 1,\n      \"quantity_ordered\": 2,\n      \"unit_price\": 2500.00\n    },\n    {\n      \"product_id\": 2,\n      \"quantity_ordered\": 5,\n      \"unit_price\": 150.00\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/sales-orders", "host": ["{{baseUrl}}"], "path": ["api", "sales-orders"]}}}, {"name": "Create Sales Order - Without Prices", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_name\": \"محمد سالم الدوسري\",\n  \"customer_phone\": \"+966506789012\",\n  \"delivery_date\": \"2025-01-15T10:00:00.000Z\",\n  \"notes\": \"طلب للمنزل الذكي\",\n  \"items\": [\n    {\n      \"product_id\": 3,\n      \"quantity_ordered\": 3\n    },\n    {\n      \"product_id\": 4,\n      \"quantity_ordered\": 2\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/sales-orders", "host": ["{{baseUrl}}"], "path": ["api", "sales-orders"]}}}, {"name": "Update Sales Order Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"PARTIALLY_DELIVERED\",\n  \"notes\": \"تم تسليم جزء من الطلب للعميل\"\n}"}, "url": {"raw": "{{baseUrl}}/api/sales-orders/1", "host": ["{{baseUrl}}"], "path": ["api", "sales-orders", "1"]}}}, {"name": "Update Sales Order Customer Info", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_phone\": \"+966507890123\",\n  \"delivery_date\": \"2025-01-20T16:00:00.000Z\",\n  \"notes\": \"تم تحديث رقم الهاتف وموعد التسليم\"\n}"}, "url": {"raw": "{{baseUrl}}/api/sales-orders/1", "host": ["{{baseUrl}}"], "path": ["api", "sales-orders", "1"]}}}, {"name": "Deliver Products - Partial", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"warehouse_id\": 1,\n  \"items\": [\n    {\n      \"product_id\": 1,\n      \"quantity_delivered\": 1\n    },\n    {\n      \"product_id\": 2,\n      \"quantity_delivered\": 3\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/sales-orders/1/deliver", "host": ["{{baseUrl}}"], "path": ["api", "sales-orders", "1", "deliver"]}}}, {"name": "Deliver Products - Complete", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"warehouse_id\": 1,\n  \"items\": [\n    {\n      \"product_id\": 1,\n      \"quantity_delivered\": 1\n    },\n    {\n      \"product_id\": 2,\n      \"quantity_delivered\": 2\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/sales-orders/1/deliver", "host": ["{{baseUrl}}"], "path": ["api", "sales-orders", "1", "deliver"]}}}, {"name": "Delete Sales Order", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/sales-orders/1", "host": ["{{baseUrl}}"], "path": ["api", "sales-orders", "1"]}}}, {"name": "Get Sales Orders Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/sales-orders/stats", "host": ["{{baseUrl}}"], "path": ["api", "sales-orders", "stats"]}}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}