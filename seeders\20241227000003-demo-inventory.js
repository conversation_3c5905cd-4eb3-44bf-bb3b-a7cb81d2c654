'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Get actual product and warehouse IDs from database
    const products = await queryInterface.sequelize.query(
      'SELECT id FROM products ORDER BY id LIMIT 5',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    const warehouses = await queryInterface.sequelize.query(
      'SELECT id FROM warehouses ORDER BY id LIMIT 3',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    if (products.length > 0 && warehouses.length > 0) {
      await queryInterface.bulkInsert('inventory', [
        // Warehouse 1 - Main warehouse
        {
          product_id: products[0].id,
          warehouse_id: warehouses[0].id,
          quantity: 15,
          minimum_quantity: 5,
          last_checked_at: new Date(),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          product_id: products[1].id,
          warehouse_id: warehouses[0].id,
          quantity: 30,
          minimum_quantity: 10,
          last_checked_at: new Date(),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          product_id: products[2].id,
          warehouse_id: warehouses[0].id,
          quantity: 5,
          minimum_quantity: 3,
          last_checked_at: new Date(),
          created_at: new Date(),
          updated_at: new Date()
        },

        // Warehouse 2 - Jeddah
        {
          product_id: products[0].id,
          warehouse_id: warehouses[1].id,
          quantity: 10,
          minimum_quantity: 3,
          last_checked_at: new Date(),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          product_id: products[3].id,
          warehouse_id: warehouses[1].id,
          quantity: 12,
          minimum_quantity: 5,
          last_checked_at: new Date(),
          created_at: new Date(),
          updated_at: new Date()
        },

        // Warehouse 3 - Dammam
        {
          product_id: products[1].id,
          warehouse_id: warehouses[2].id,
          quantity: 25,
          minimum_quantity: 10,
          last_checked_at: new Date(),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          product_id: products[4].id,
          warehouse_id: warehouses[2].id,
          quantity: 8,
          minimum_quantity: 5,
          last_checked_at: new Date(),
          created_at: new Date(),
          updated_at: new Date()
        },

        // Out of stock examples
        {
          product_id: products[2].id,
          warehouse_id: warehouses[1].id,
          quantity: 0,
          minimum_quantity: 2,
          last_checked_at: new Date(),
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          product_id: products[3].id,
          warehouse_id: warehouses[2].id,
          quantity: 0,
          minimum_quantity: 3,
          last_checked_at: new Date(),
          created_at: new Date(),
          updated_at: new Date()
        }
      ], {});
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('inventory', null, {});
  }
};
