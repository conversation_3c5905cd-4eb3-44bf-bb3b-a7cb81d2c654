import api from './api';

export const dashboardService = {
  // Get main KPIs
  getKPIs: async () => {
    const response = await api.get('/reports/kpis');
    return response.data;
  },

  // Get sales trend (6 months)
  getSalesTrend: async () => {
    const response = await api.get('/reports/sales-trend');
    return response.data;
  },

  // Get top products
  getTopProducts: async (limit = 5) => {
    const response = await api.get('/reports/top-products', { 
      params: { limit } 
    });
    return response.data;
  },

  // Get top suppliers
  getTopSuppliers: async (limit = 5) => {
    const response = await api.get('/reports/top-suppliers', { 
      params: { limit } 
    });
    return response.data;
  },

  // Get inventory alerts
  getInventoryAlerts: async () => {
    const response = await api.get('/reports/inventory-alerts');
    return response.data;
  },

  // Get orders status
  getOrdersStatus: async () => {
    const response = await api.get('/reports/orders-status');
    return response.data;
  },

  // Get complete dashboard data
  getDashboard: async () => {
    const response = await api.get('/reports/dashboard');
    return response.data;
  }
};

// Additional services for other entities
export const supplierService = {
  getAll: async (params = {}) => {
    const response = await api.get('/suppliers', { params });
    return response.data;
  },

  getById: async (id) => {
    const response = await api.get(`/suppliers/${id}`);
    return response.data;
  },

  create: async (data) => {
    const response = await api.post('/suppliers', data);
    return response.data;
  },

  update: async (id, data) => {
    const response = await api.put(`/suppliers/${id}`, data);
    return response.data;
  },

  delete: async (id) => {
    const response = await api.delete(`/suppliers/${id}`);
    return response.data;
  },

  getStats: async () => {
    const response = await api.get('/suppliers/stats');
    return response.data;
  }
};

export const warehouseService = {
  getAll: async (params = {}) => {
    const response = await api.get('/warehouses', { params });
    return response.data;
  },

  getById: async (id) => {
    const response = await api.get(`/warehouses/${id}`);
    return response.data;
  },

  create: async (data) => {
    const response = await api.post('/warehouses', data);
    return response.data;
  },

  update: async (id, data) => {
    const response = await api.put(`/warehouses/${id}`, data);
    return response.data;
  },

  delete: async (id) => {
    const response = await api.delete(`/warehouses/${id}`);
    return response.data;
  },

  getStats: async () => {
    const response = await api.get('/warehouses/stats');
    return response.data;
  }
};

export const inventoryService = {
  getAll: async (params = {}) => {
    const response = await api.get('/inventory', { params });
    return response.data;
  },

  getById: async (id) => {
    const response = await api.get(`/inventory/${id}`);
    return response.data;
  },

  create: async (data) => {
    const response = await api.post('/inventory', data);
    return response.data;
  },

  update: async (id, data) => {
    const response = await api.put(`/inventory/${id}`, data);
    return response.data;
  },

  delete: async (id) => {
    const response = await api.delete(`/inventory/${id}`);
    return response.data;
  },

  getStats: async () => {
    const response = await api.get('/inventory/stats');
    return response.data;
  }
};

export const alertService = {
  getAll: async (params = {}) => {
    const response = await api.get('/alerts', { params });
    return response.data;
  },

  getActive: async () => {
    const response = await api.get('/alerts/active');
    return response.data;
  },

  getById: async (id) => {
    const response = await api.get(`/alerts/${id}`);
    return response.data;
  },

  markAsSeen: async (id) => {
    const response = await api.put(`/alerts/${id}/seen`);
    return response.data;
  },

  resolve: async (id) => {
    const response = await api.put(`/alerts/${id}/resolve`);
    return response.data;
  },

  bulkResolve: async (ids) => {
    const response = await api.post('/alerts/bulk-resolve', { ids });
    return response.data;
  },

  checkAll: async () => {
    const response = await api.post('/alerts/check-all');
    return response.data;
  },

  getStats: async () => {
    const response = await api.get('/alerts/stats');
    return response.data;
  }
};
