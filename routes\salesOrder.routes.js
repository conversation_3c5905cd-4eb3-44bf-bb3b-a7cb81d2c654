const express = require('express');
const router = express.Router();
const salesOrderController = require('../controllers/salesOrder.controller');

// Middleware for request logging
const requestLogger = (req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.originalUrl}`);
  next();
};

// Apply logging middleware to all routes
router.use(requestLogger);

// Validation middleware for sales order creation
const validateSalesOrderData = (req, res, next) => {
  const { customer_name, items } = req.body;
  
  // Check required fields for POST requests
  if (req.method === 'POST') {
    if (!customer_name || typeof customer_name !== 'string' || customer_name.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'اسم العميل مطلوب ويجب أن يكون على الأقل حرفين',
        timestamp: new Date().toISOString()
      });
    }
    
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'عناصر الطلب مطلوبة ويجب أن تكون مصفوفة غير فارغة',
        timestamp: new Date().toISOString()
      });
    }
    
    // Validate each item
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      
      if (!item.product_id || typeof item.product_id !== 'number') {
        return res.status(400).json({
          success: false,
          message: `معرف المنتج مطلوب للعنصر ${i + 1}`,
          timestamp: new Date().toISOString()
        });
      }
      
      if (!item.quantity_ordered || typeof item.quantity_ordered !== 'number' || item.quantity_ordered < 1) {
        return res.status(400).json({
          success: false,
          message: `الكمية المطلوبة يجب أن تكون رقم أكبر من 0 للعنصر ${i + 1}`,
          timestamp: new Date().toISOString()
        });
      }
      
      if (item.unit_price !== undefined && (typeof item.unit_price !== 'number' || item.unit_price < 0)) {
        return res.status(400).json({
          success: false,
          message: `سعر الوحدة يجب أن يكون رقم أكبر من أو يساوي 0 للعنصر ${i + 1}`,
          timestamp: new Date().toISOString()
        });
      }
    }
  }
  
  // Validate customer_name if provided
  if (customer_name && (typeof customer_name !== 'string' || customer_name.trim().length < 2)) {
    return res.status(400).json({
      success: false,
      message: 'اسم العميل يجب أن يكون نص بطول حرفين على الأقل',
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

// Validation middleware for delivering products
const validateDeliveryData = (req, res, next) => {
  const { items } = req.body;
  
  if (!items || !Array.isArray(items) || items.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'عناصر التسليم مطلوبة ويجب أن تكون مصفوفة غير فارغة',
      timestamp: new Date().toISOString()
    });
  }
  
  // Validate each item
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    
    if (!item.product_id || typeof item.product_id !== 'number') {
      return res.status(400).json({
        success: false,
        message: `معرف المنتج مطلوب للعنصر ${i + 1}`,
        timestamp: new Date().toISOString()
      });
    }
    
    if (!item.quantity_delivered || typeof item.quantity_delivered !== 'number' || item.quantity_delivered < 1) {
      return res.status(400).json({
        success: false,
        message: `الكمية المسلمة يجب أن تكون رقم أكبر من 0 للعنصر ${i + 1}`,
        timestamp: new Date().toISOString()
      });
    }
  }
  
  next();
};

// Validation middleware for ID parameter
const validateIdParam = (req, res, next) => {
  const { id } = req.params;
  
  if (!id || isNaN(parseInt(id))) {
    return res.status(400).json({
      success: false,
      message: 'معرف أمر البيع يجب أن يكون رقم صحيح',
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

// Routes

/**
 * @route   GET /api/sales-orders
 * @desc    Get all sales orders with optional filtering and pagination
 * @access  Public
 * @params  ?page=1&limit=10&customer_name=أحمد&status=NEW&start_date=2024-01-01&end_date=2024-12-31
 */
router.get('/', salesOrderController.getAllSalesOrders);

/**
 * @route   GET /api/sales-orders/stats
 * @desc    Get sales orders statistics
 * @access  Public
 */
router.get('/stats', salesOrderController.getSalesOrdersStats);

/**
 * @route   GET /api/sales-orders/:id
 * @desc    Get single sales order by ID
 * @access  Public
 */
router.get('/:id', validateIdParam, salesOrderController.getSalesOrderById);

/**
 * @route   POST /api/sales-orders
 * @desc    Create new sales order
 * @access  Public
 * @body    { customer_name, customer_phone?, delivery_date?, notes?, items: [{ product_id, quantity_ordered, unit_price? }] }
 */
router.post('/', validateSalesOrderData, salesOrderController.createSalesOrder);

/**
 * @route   PUT /api/sales-orders/:id
 * @desc    Update sales order by ID
 * @access  Public
 * @body    { customer_name?, customer_phone?, status?, delivery_date?, notes?, total_amount? }
 */
router.put('/:id', validateIdParam, salesOrderController.updateSalesOrder);

/**
 * @route   DELETE /api/sales-orders/:id
 * @desc    Delete sales order by ID (only NEW orders)
 * @access  Public
 */
router.delete('/:id', validateIdParam, salesOrderController.deleteSalesOrder);

/**
 * @route   POST /api/sales-orders/:id/deliver
 * @desc    Deliver products for sales order and update inventory
 * @access  Public
 * @body    { items: [{ product_id, quantity_delivered }], warehouse_id? }
 */
router.post('/:id/deliver', validateIdParam, validateDeliveryData, salesOrderController.deliverProducts);

module.exports = router;
