'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkInsert('suppliers', [
      {
        name: 'شركة التقنية المتقدمة',
        contact_name: 'أحم<PERSON> محمد علي',
        phone: '966501234567',
        email: '<EMAIL>',
        tax_number: 'TAX-001-2024',
        address: 'الرياض، حي الملك فهد، شارع الملك عبدالعزيز، مبنى رقم 123',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'مؤسسة الحاسوب الذكي',
        contact_name: 'فاطمة أحمد السالم',
        phone: '966502345678',
        email: '<EMAIL>',
        tax_number: 'TAX-002-2024',
        address: 'جدة، حي الروضة، طريق الملك عبدالله، مجمع الأعمال التجاري',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'شركة الإلكترونيات الحديثة',
        contact_name: 'محمد عبدالله الخالد',
        phone: '966503456789',
        email: '<EMAIL>',
        tax_number: 'TAX-003-2024',
        address: 'الدمام، حي الفيصلية، شارع الأمير محمد بن فهد، برج التجارة',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'مجموعة الأجهزة المكتبية',
        contact_name: 'سارة محمد الأحمد',
        phone: '966504567890',
        email: '<EMAIL>',
        tax_number: 'TAX-004-2024',
        address: 'مكة المكرمة، حي العزيزية، طريق الحرم، مركز الأعمال الإسلامي',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'شركة الشبكات والاتصالات',
        contact_name: 'عبدالرحمن سعد المطيري',
        phone: '966505678901',
        email: '<EMAIL>',
        tax_number: 'TAX-005-2024',
        address: 'المدينة المنورة، حي السلام، شارع قباء، مجمع الأنصار التجاري',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'مؤسسة الطباعة والتصوير',
        contact_name: 'نورا عبدالعزيز القحطاني',
        phone: '966506789012',
        email: '<EMAIL>',
        tax_number: 'TAX-006-2024',
        address: 'الطائف، حي الشفا، طريق الرياض، مركز الطائف التجاري',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'شركة الملحقات التقنية',
        contact_name: 'خالد أحمد الزهراني',
        phone: '966507890123',
        email: '<EMAIL>',
        tax_number: 'TAX-007-2024',
        address: 'أبها، حي المنهل، شارع الملك فيصل، برج عسير التجاري',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'مجموعة الحلول الرقمية',
        contact_name: 'ريم محمد الغامدي',
        phone: '966508901234',
        email: '<EMAIL>',
        tax_number: 'TAX-008-2024',
        address: 'تبوك، حي السليمانية، طريق الأمير فهد بن سلطان، مول تبوك',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'شركة الأنظمة المتكاملة',
        contact_name: 'عمر سليمان العتيبي',
        phone: '966509012345',
        email: '<EMAIL>',
        tax_number: 'TAX-009-2024',
        address: 'حائل، حي الوسيط، شارع الملك عبدالعزيز، مركز حائل التجاري',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'مؤسسة التجهيزات المكتبية',
        contact_name: 'هند عبدالله الشهري',
        phone: '966500123456',
        email: '<EMAIL>',
        tax_number: 'TAX-010-2024',
        address: 'جازان، حي الروضة، طريق الملك فهد، مجمع جازان التجاري',
        created_at: new Date(),
        updated_at: new Date()
      }
    ], {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('suppliers', null, {});
  }
};
