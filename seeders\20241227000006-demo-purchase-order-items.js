'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Get actual purchase order and product IDs from database
    const purchaseOrders = await queryInterface.sequelize.query(
      'SELECT id FROM purchase_orders ORDER BY id LIMIT 3',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );
    
    const products = await queryInterface.sequelize.query(
      'SELECT id FROM products ORDER BY id LIMIT 5',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );
    
    if (purchaseOrders.length > 0 && products.length > 0) {
      await queryInterface.bulkInsert('purchase_order_items', [
        // Items for first purchase order (NEW)
        {
          purchase_order_id: purchaseOrders[0].id,
          product_id: products[0].id,
          quantity_ordered: 10,
          quantity_received: 0,
          unit_price: 2500.00,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          purchase_order_id: purchaseOrders[0].id,
          product_id: products[1].id,
          quantity_ordered: 20,
          quantity_received: 0,
          unit_price: 150.00,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          purchase_order_id: purchaseOrders[0].id,
          product_id: products[2].id,
          quantity_ordered: 5,
          quantity_received: 0,
          unit_price: 800.00,
          created_at: new Date(),
          updated_at: new Date()
        },
        
        // Items for second purchase order (PARTIALLY_DELIVERED)
        {
          purchase_order_id: purchaseOrders[1].id,
          product_id: products[1].id,
          quantity_ordered: 15,
          quantity_received: 8,
          unit_price: 150.00,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          purchase_order_id: purchaseOrders[1].id,
          product_id: products[3].id,
          quantity_ordered: 8,
          quantity_received: 3,
          unit_price: 600.00,
          created_at: new Date(),
          updated_at: new Date()
        },
        
        // Items for third purchase order (COMPLETED)
        {
          purchase_order_id: purchaseOrders[2].id,
          product_id: products[0].id,
          quantity_ordered: 3,
          quantity_received: 3,
          unit_price: 2500.00,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          purchase_order_id: purchaseOrders[2].id,
          product_id: products[4].id,
          quantity_ordered: 12,
          quantity_received: 12,
          unit_price: 375.00,
          created_at: new Date(),
          updated_at: new Date()
        }
      ], {});
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('purchase_order_items', null, {});
  }
};
