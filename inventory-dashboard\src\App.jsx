import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/Layout/Layout';
import Dashboard from './pages/Dashboard';
import Products from './pages/Products';
import './App.css';

// Placeholder components for other pages
const Suppliers = () => (
  <div className="p-6">
    <h1 className="text-2xl font-bold text-gray-900 mb-4">إدارة الموردين</h1>
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <p className="text-gray-600">صفحة إدارة الموردين قيد التطوير...</p>
    </div>
  </div>
);

const Warehouses = () => (
  <div className="p-6">
    <h1 className="text-2xl font-bold text-gray-900 mb-4">إدارة المستودعات</h1>
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <p className="text-gray-600">صفحة إدارة المستودعات قيد التطوير...</p>
    </div>
  </div>
);

const Inventory = () => (
  <div className="p-6">
    <h1 className="text-2xl font-bold text-gray-900 mb-4">إدارة الجرد</h1>
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <p className="text-gray-600">صفحة إدارة الجرد قيد التطوير...</p>
    </div>
  </div>
);

const Movements = () => (
  <div className="p-6">
    <h1 className="text-2xl font-bold text-gray-900 mb-4">حركة المخزون</h1>
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <p className="text-gray-600">صفحة حركة المخزون قيد التطوير...</p>
    </div>
  </div>
);

const PurchaseOrders = () => (
  <div className="p-6">
    <h1 className="text-2xl font-bold text-gray-900 mb-4">أوامر الشراء</h1>
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <p className="text-gray-600">صفحة أوامر الشراء قيد التطوير...</p>
    </div>
  </div>
);

const SalesOrders = () => (
  <div className="p-6">
    <h1 className="text-2xl font-bold text-gray-900 mb-4">أوامر البيع</h1>
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <p className="text-gray-600">صفحة أوامر البيع قيد التطوير...</p>
    </div>
  </div>
);

const Reports = () => (
  <div className="p-6">
    <h1 className="text-2xl font-bold text-gray-900 mb-4">التقارير</h1>
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <p className="text-gray-600">صفحة التقارير قيد التطوير...</p>
    </div>
  </div>
);

const Alerts = () => (
  <div className="p-6">
    <h1 className="text-2xl font-bold text-gray-900 mb-4">التنبيهات</h1>
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <p className="text-gray-600">صفحة التنبيهات قيد التطوير...</p>
    </div>
  </div>
);

function App() {
  return (
    <Router>
      <div className="app-container">
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/" element={<Layout />}>
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="products" element={<Products />} />
            <Route path="suppliers" element={<Suppliers />} />
            <Route path="warehouses" element={<Warehouses />} />
            <Route path="inventory" element={<Inventory />} />
            <Route path="movements" element={<Movements />} />
            <Route path="purchase-orders" element={<PurchaseOrders />} />
            <Route path="sales-orders" element={<SalesOrders />} />
            <Route path="reports" element={<Reports />} />
            <Route path="alerts" element={<Alerts />} />
          </Route>
        </Routes>
      </div>
    </Router>
  );
}

export default App;
