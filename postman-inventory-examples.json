{"info": {"name": "Inventory Management API", "description": "مجموعة أمثلة لاختبار API إدارة الجرد", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Inventory", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/inventory", "host": ["{{baseUrl}}"], "path": ["api", "inventory"]}}}, {"name": "Get Inventory with Pagination", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/inventory?page=1&limit=5", "host": ["{{baseUrl}}"], "path": ["api", "inventory"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "5"}]}}}, {"name": "Get Inventory by Product", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/inventory?product_id=1", "host": ["{{baseUrl}}"], "path": ["api", "inventory"], "query": [{"key": "product_id", "value": "1"}]}}}, {"name": "Get Inventory by Warehouse", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/inventory?warehouse_id=1", "host": ["{{baseUrl}}"], "path": ["api", "inventory"], "query": [{"key": "warehouse_id", "value": "1"}]}}}, {"name": "Get Low Stock Items", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/inventory?low_stock=true", "host": ["{{baseUrl}}"], "path": ["api", "inventory"], "query": [{"key": "low_stock", "value": "true"}]}}}, {"name": "Get Out of Stock Items", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/inventory?out_of_stock=true", "host": ["{{baseUrl}}"], "path": ["api", "inventory"], "query": [{"key": "out_of_stock", "value": "true"}]}}}, {"name": "Get Inventory by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/inventory/1", "host": ["{{baseUrl}}"], "path": ["api", "inventory", "1"]}}}, {"name": "Create Inventory Record", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 1,\n  \"warehouse_id\": 2,\n  \"quantity\": 25,\n  \"minimum_quantity\": 5,\n  \"last_checked_at\": \"2024-12-27T10:00:00.000Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/inventory", "host": ["{{baseUrl}}"], "path": ["api", "inventory"]}}}, {"name": "Create Inventory - No Minimum", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 3,\n  \"warehouse_id\": 4,\n  \"quantity\": 10\n}"}, "url": {"raw": "{{baseUrl}}/api/inventory", "host": ["{{baseUrl}}"], "path": ["api", "inventory"]}}}, {"name": "Update Inventory Quantity", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 30,\n  \"minimum_quantity\": 8\n}"}, "url": {"raw": "{{baseUrl}}/api/inventory/1", "host": ["{{baseUrl}}"], "path": ["api", "inventory", "1"]}}}, {"name": "Update Last Checked Date", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"last_checked_at\": \"2024-12-27T15:30:00.000Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/inventory/1", "host": ["{{baseUrl}}"], "path": ["api", "inventory", "1"]}}}, {"name": "Delete Inventory Record", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/inventory/1", "host": ["{{baseUrl}}"], "path": ["api", "inventory", "1"]}}}, {"name": "Get Inventory Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/inventory/stats", "host": ["{{baseUrl}}"], "path": ["api", "inventory", "stats"]}}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}