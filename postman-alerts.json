{"info": {"name": "Smart Inventory Alerts API", "description": "مجموعة أمثلة لاختبار API التنبيهات الذكية للمخزون", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Alerts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/alerts", "host": ["{{baseUrl}}"], "path": ["api", "alerts"]}, "description": "جلب جميع التنبيهات مع إمكانية التصفية والتصفح"}}, {"name": "Get Alerts with Pagination", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/alerts?page=1&limit=5", "host": ["{{baseUrl}}"], "path": ["api", "alerts"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "5"}]}, "description": "جلب التنبيهات مع التصفح"}}, {"name": "Get Critical Alerts Only", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/alerts?level=CRITICAL", "host": ["{{baseUrl}}"], "path": ["api", "alerts"], "query": [{"key": "level", "value": "CRITICAL"}]}, "description": "جلب التنبيهات الحرجة فقط"}}, {"name": "Get New Alerts Only", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/alerts?status=NEW", "host": ["{{baseUrl}}"], "path": ["api", "alerts"], "query": [{"key": "status", "value": "NEW"}]}, "description": "جلب التنبيهات الجديدة فقط"}}, {"name": "Get Out of Stock Alerts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/alerts?type=OUT_OF_STOCK", "host": ["{{baseUrl}}"], "path": ["api", "alerts"], "query": [{"key": "type", "value": "OUT_OF_STOCK"}]}, "description": "جلب تنبيهات نفاد المخزون فقط"}}, {"name": "Get Active Alerts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/alerts/active", "host": ["{{baseUrl}}"], "path": ["api", "alerts", "active"]}, "description": "جلب التنبيهات النشطة (جديدة ومشاهدة) مجمعة حسب المستوى"}}, {"name": "Get Alert Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/alerts/stats", "host": ["{{baseUrl}}"], "path": ["api", "alerts", "stats"]}, "description": "جلب إحصائيات التنبيهات والمنتجات الأكثر إشكالية"}}, {"name": "Get Alert by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/alerts/1", "host": ["{{baseUrl}}"], "path": ["api", "alerts", "1"]}, "description": "جلب تنبيه واحد بالمعرف (يتم تحديد حالته إلى مشاهد تلقائياً)"}}, {"name": "<PERSON> as Seen", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/api/alerts/1/seen", "host": ["{{baseUrl}}"], "path": ["api", "alerts", "1", "seen"]}, "description": "تحديد التنبيه كمشاهد"}}, {"name": "Resolve <PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"resolution_note\": \"تم تجديد المخزون من المورد الرئيسي\"\n}"}, "url": {"raw": "{{baseUrl}}/api/alerts/1/resolve", "host": ["{{baseUrl}}"], "path": ["api", "alerts", "1", "resolve"]}, "description": "حل التنبيه مع ملاحظة اختيارية"}}, {"name": "Resolve <PERSON><PERSON> without Note", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/api/alerts/2/resolve", "host": ["{{baseUrl}}"], "path": ["api", "alerts", "2", "resolve"]}, "description": "حل التنبيه بدون ملاحظة"}}, {"name": "Bulk Resolve Alerts", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"alert_ids\": [1, 2, 3],\n  \"resolution_note\": \"تم حل جميع التنبيهات بعد تجديد المخزون\"\n}"}, "url": {"raw": "{{baseUrl}}/api/alerts/bulk-resolve", "host": ["{{baseUrl}}"], "path": ["api", "alerts", "bulk-resolve"]}, "description": "حل عدة تنبيهات دفعة واحدة"}}, {"name": "Check All Inventory for Al<PERSON>s", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/alerts/check-all", "host": ["{{baseUrl}}"], "path": ["api", "alerts", "check-all"]}, "description": "فحص جميع عناصر المخزون وإنشاء/حل التنبيهات حسب الحاجة"}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set timestamp for request tracking", "pm.globals.set('requestTimestamp', new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Basic response validation", "pm.test('Status code is 200 or 201', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test('Response has success field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "});", "", "pm.test('Response has data field when successful', function () {", "    const jsonData = pm.response.json();", "    if (jsonData.success) {", "        pm.expect(jsonData).to.have.property('data');", "    }", "});", "", "pm.test('Response time is less than 3000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});", "", "// Alert-specific tests", "if (pm.request.url.path.includes('alerts')) {", "    pm.test('Alert data structure is valid', function () {", "        const jsonData = pm.response.json();", "        if (jsonData.success && jsonData.data.alerts) {", "            const alerts = Array.isArray(jsonData.data.alerts) ? jsonData.data.alerts : [jsonData.data.alert];", "            alerts.forEach(alert => {", "                pm.expect(alert).to.have.property('id');", "                pm.expect(alert).to.have.property('type');", "                pm.expect(alert).to.have.property('level');", "                pm.expect(alert).to.have.property('status');", "                pm.expect(alert.type).to.be.oneOf(['LOW_STOCK', 'OUT_OF_STOCK']);", "                pm.expect(alert.level).to.be.oneOf(['CRITICAL', 'HIGH', 'MEDIUM']);", "                pm.expect(alert.status).to.be.oneOf(['NEW', 'SEEN', 'RESOLVED']);", "            });", "        }", "    });", "}"]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}