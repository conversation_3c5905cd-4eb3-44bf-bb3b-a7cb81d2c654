'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class SalesOrderItem extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Define associations here
      SalesOrderItem.belongsTo(models.SalesOrder, {
        foreignKey: 'sales_order_id',
        as: 'salesOrder'
      });
      
      SalesOrderItem.belongsTo(models.Product, {
        foreignKey: 'product_id',
        as: 'product'
      });
    }

    // Instance method to check if item is fully delivered
    isFullyDelivered() {
      return this.quantity_delivered >= this.quantity_ordered;
    }

    // Instance method to check if item is partially delivered
    isPartiallyDelivered() {
      return this.quantity_delivered > 0 && this.quantity_delivered < this.quantity_ordered;
    }

    // Instance method to get remaining quantity
    getRemainingQuantity() {
      return Math.max(0, this.quantity_ordered - this.quantity_delivered);
    }

    // Instance method to get completion percentage
    getCompletionPercentage() {
      return this.quantity_ordered > 0 
        ? Math.round((this.quantity_delivered / this.quantity_ordered) * 100) 
        : 0;
    }

    // Instance method to get total value
    getTotalValue() {
      return this.unit_price ? this.quantity_ordered * this.unit_price : 0;
    }

    // Instance method to get delivered value
    getDeliveredValue() {
      return this.unit_price ? this.quantity_delivered * this.unit_price : 0;
    }

    // Instance method to get item summary
    getItemSummary() {
      return {
        id: this.id,
        product_id: this.product_id,
        quantity_ordered: this.quantity_ordered,
        quantity_delivered: this.quantity_delivered,
        remaining_quantity: this.getRemainingQuantity(),
        completion_percentage: this.getCompletionPercentage(),
        unit_price: this.unit_price,
        total_value: this.getTotalValue(),
        delivered_value: this.getDeliveredValue()
      };
    }

    // Static method to find items by sales order
    static async findBySalesOrder(salesOrderId) {
      return await this.findAll({
        where: {
          sales_order_id: salesOrderId
        },
        include: [
          {
            model: sequelize.models.Product,
            as: 'product'
          }
        ],
        order: [['created_at', 'ASC']]
      });
    }

    // Static method to find items by product
    static async findByProduct(productId) {
      return await this.findAll({
        where: {
          product_id: productId
        },
        include: [
          {
            model: sequelize.models.SalesOrder,
            as: 'salesOrder'
          }
        ],
        order: [['created_at', 'DESC']]
      });
    }

    // Static method to get pending items (not fully delivered)
    static async getPendingItems() {
      return await this.findAll({
        where: {
          quantity_delivered: {
            [sequelize.Sequelize.Op.lt]: sequelize.Sequelize.col('quantity_ordered')
          }
        },
        include: [
          {
            model: sequelize.models.Product,
            as: 'product'
          },
          {
            model: sequelize.models.SalesOrder,
            as: 'salesOrder'
          }
        ],
        order: [['created_at', 'ASC']]
      });
    }

    // Static method to get top selling products
    static async getTopSellingProducts(limit = 10) {
      return await this.findAll({
        attributes: [
          'product_id',
          [sequelize.Sequelize.fn('SUM', sequelize.Sequelize.col('quantity_ordered')), 'total_ordered'],
          [sequelize.Sequelize.fn('SUM', sequelize.Sequelize.col('quantity_delivered')), 'total_delivered'],
          [sequelize.Sequelize.fn('COUNT', sequelize.Sequelize.col('SalesOrderItem.id')), 'order_count']
        ],
        include: [
          {
            model: sequelize.models.Product,
            as: 'product',
            attributes: ['name', 'code']
          }
        ],
        group: ['product_id', 'product.id'],
        order: [[sequelize.Sequelize.fn('SUM', sequelize.Sequelize.col('quantity_ordered')), 'DESC']],
        limit: limit
      });
    }
  }

  SalesOrderItem.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    sales_order_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'sales_orders',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'معرف أمر البيع مطلوب'
        },
        isInt: {
          msg: 'معرف أمر البيع يجب أن يكون رقم صحيح'
        }
      }
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'products',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'معرف المنتج مطلوب'
        },
        isInt: {
          msg: 'معرف المنتج يجب أن يكون رقم صحيح'
        }
      }
    },
    quantity_ordered: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        notNull: {
          msg: 'الكمية المطلوبة مطلوبة'
        },
        min: {
          args: [1],
          msg: 'الكمية المطلوبة يجب أن تكون أكبر من أو تساوي 1'
        },
        isInt: {
          msg: 'الكمية المطلوبة يجب أن تكون رقم صحيح'
        }
      }
    },
    quantity_delivered: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: {
          args: [0],
          msg: 'الكمية المسلمة يجب أن تكون أكبر من أو تساوي 0'
        },
        isInt: {
          msg: 'الكمية المسلمة يجب أن تكون رقم صحيح'
        }
      }
    },
    unit_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      validate: {
        min: {
          args: [0],
          msg: 'سعر الوحدة يجب أن يكون أكبر من أو يساوي 0'
        }
      }
    }
  }, {
    sequelize,
    modelName: 'SalesOrderItem',
    tableName: 'sales_order_items',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['sales_order_id']
      },
      {
        fields: ['product_id']
      },
      {
        fields: ['quantity_ordered']
      },
      {
        fields: ['quantity_delivered']
      },
      {
        unique: true,
        fields: ['sales_order_id', 'product_id']
      }
    ]
  });

  return SalesOrderItem;
};
