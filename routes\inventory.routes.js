const express = require('express');
const router = express.Router();
const inventoryController = require('../controllers/inventory.controller');

// Middleware for request logging
const requestLogger = (req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.originalUrl}`);
  next();
};

// Apply logging middleware to all routes
router.use(requestLogger);

// Validation middleware for inventory creation/update
const validateInventoryData = (req, res, next) => {
  const { product_id, warehouse_id, quantity, minimum_quantity } = req.body;
  
  // Check required fields for POST requests
  if (req.method === 'POST') {
    if (!product_id || !warehouse_id) {
      return res.status(400).json({
        success: false,
        message: 'معرف المنتج ومعرف المستودع مطلوبان',
        timestamp: new Date().toISOString()
      });
    }
  }
  
  // Validate product_id if provided
  if (product_id && (typeof product_id !== 'number' || product_id <= 0)) {
    return res.status(400).json({
      success: false,
      message: 'معرف المنتج يجب أن يكون رقم صحيح أكبر من 0',
      timestamp: new Date().toISOString()
    });
  }
  
  // Validate warehouse_id if provided
  if (warehouse_id && (typeof warehouse_id !== 'number' || warehouse_id <= 0)) {
    return res.status(400).json({
      success: false,
      message: 'معرف المستودع يجب أن يكون رقم صحيح أكبر من 0',
      timestamp: new Date().toISOString()
    });
  }
  
  // Validate quantity if provided
  if (quantity !== undefined) {
    if (typeof quantity !== 'number' || quantity < 0) {
      return res.status(400).json({
        success: false,
        message: 'الكمية يجب أن تكون رقم صحيح أكبر من أو يساوي 0',
        timestamp: new Date().toISOString()
      });
    }
  }
  
  // Validate minimum_quantity if provided
  if (minimum_quantity !== undefined && minimum_quantity !== null) {
    if (typeof minimum_quantity !== 'number' || minimum_quantity < 0) {
      return res.status(400).json({
        success: false,
        message: 'الحد الأدنى للكمية يجب أن يكون رقم صحيح أكبر من أو يساوي 0',
        timestamp: new Date().toISOString()
      });
    }
  }
  
  next();
};

// Validation middleware for ID parameter
const validateIdParam = (req, res, next) => {
  const { id } = req.params;
  
  if (!id || isNaN(parseInt(id))) {
    return res.status(400).json({
      success: false,
      message: 'معرف سجل الجرد يجب أن يكون رقم صحيح',
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

// Routes

/**
 * @route   GET /api/inventory
 * @desc    Get all inventory records with optional filtering and pagination
 * @access  Public
 * @params  ?page=1&limit=10&product_id=1&warehouse_id=1&low_stock=true&out_of_stock=true
 */
router.get('/', inventoryController.getAllInventory);

/**
 * @route   GET /api/inventory/stats
 * @desc    Get inventory statistics
 * @access  Public
 */
router.get('/stats', inventoryController.getInventoryStats);

/**
 * @route   GET /api/inventory/:id
 * @desc    Get single inventory record by ID
 * @access  Public
 */
router.get('/:id', validateIdParam, inventoryController.getInventoryById);

/**
 * @route   POST /api/inventory
 * @desc    Create new inventory record
 * @access  Public
 * @body    { product_id, warehouse_id, quantity?, minimum_quantity?, last_checked_at? }
 */
router.post('/', validateInventoryData, inventoryController.createInventory);

/**
 * @route   PUT /api/inventory/:id
 * @desc    Update inventory record by ID
 * @access  Public
 * @body    { quantity?, minimum_quantity?, last_checked_at? }
 */
router.put('/:id', validateIdParam, validateInventoryData, inventoryController.updateInventory);

/**
 * @route   DELETE /api/inventory/:id
 * @desc    Delete inventory record by ID
 * @access  Public
 */
router.delete('/:id', validateIdParam, inventoryController.deleteInventory);

module.exports = router;
