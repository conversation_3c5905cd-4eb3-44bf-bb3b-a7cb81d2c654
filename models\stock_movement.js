'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class StockMovement extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Define associations here
      StockMovement.belongsTo(models.Product, {
        foreignKey: 'product_id',
        as: 'product'
      });
      
      StockMovement.belongsTo(models.Warehouse, {
        foreignKey: 'warehouse_id',
        as: 'warehouse'
      });
    }

    // Instance method to check if movement is inbound
    isInbound() {
      return this.type === 'IN';
    }

    // Instance method to check if movement is outbound
    isOutbound() {
      return this.type === 'OUT';
    }

    // Instance method to get movement summary
    getMovementSummary() {
      return {
        id: this.id,
        product_id: this.product_id,
        warehouse_id: this.warehouse_id,
        type: this.type,
        quantity: this.quantity,
        reference: this.reference,
        moved_at: this.moved_at
      };
    }

    // Static method to find movements by product
    static async findByProduct(productId) {
      return await this.findAll({
        where: {
          product_id: productId
        },
        include: [
          {
            model: sequelize.models.Warehouse,
            as: 'warehouse'
          }
        ],
        order: [['moved_at', 'DESC']]
      });
    }

    // Static method to find movements by warehouse
    static async findByWarehouse(warehouseId) {
      return await this.findAll({
        where: {
          warehouse_id: warehouseId
        },
        include: [
          {
            model: sequelize.models.Product,
            as: 'product'
          }
        ],
        order: [['moved_at', 'DESC']]
      });
    }

    // Static method to find movements by type
    static async findByType(type) {
      return await this.findAll({
        where: {
          type: type
        },
        include: [
          {
            model: sequelize.models.Product,
            as: 'product'
          },
          {
            model: sequelize.models.Warehouse,
            as: 'warehouse'
          }
        ],
        order: [['moved_at', 'DESC']]
      });
    }

    // Static method to get total quantity moved for a product
    static async getTotalMovedQuantity(productId, type = null) {
      const whereClause = { product_id: productId };
      if (type) {
        whereClause.type = type;
      }
      
      const result = await this.sum('quantity', {
        where: whereClause
      });
      return result || 0;
    }

    // Static method to get movements within date range
    static async findByDateRange(startDate, endDate) {
      return await this.findAll({
        where: {
          moved_at: {
            [sequelize.Sequelize.Op.between]: [startDate, endDate]
          }
        },
        include: [
          {
            model: sequelize.models.Product,
            as: 'product'
          },
          {
            model: sequelize.models.Warehouse,
            as: 'warehouse'
          }
        ],
        order: [['moved_at', 'DESC']]
      });
    }
  }

  StockMovement.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'products',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'معرف المنتج مطلوب'
        },
        isInt: {
          msg: 'معرف المنتج يجب أن يكون رقم صحيح'
        }
      }
    },
    warehouse_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'warehouses',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'معرف المستودع مطلوب'
        },
        isInt: {
          msg: 'معرف المستودع يجب أن يكون رقم صحيح'
        }
      }
    },
    type: {
      type: DataTypes.ENUM('IN', 'OUT'),
      allowNull: false,
      validate: {
        notNull: {
          msg: 'نوع الحركة مطلوب'
        },
        isIn: {
          args: [['IN', 'OUT']],
          msg: 'نوع الحركة يجب أن يكون IN أو OUT'
        }
      }
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        notNull: {
          msg: 'الكمية مطلوبة'
        },
        min: {
          args: [1],
          msg: 'الكمية يجب أن تكون أكبر من أو تساوي 1'
        },
        isInt: {
          msg: 'الكمية يجب أن تكون رقم صحيح'
        }
      }
    },
    reference: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [1, 255],
          msg: 'المرجع يجب أن يكون بين 1 و 255 حرف'
        }
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    moved_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'StockMovement',
    tableName: 'stock_movements',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['product_id']
      },
      {
        fields: ['warehouse_id']
      },
      {
        fields: ['type']
      },
      {
        fields: ['moved_at']
      },
      {
        fields: ['product_id', 'warehouse_id']
      }
    ]
  });

  return StockMovement;
};
