'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('products', 'minimum_quantity', {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: 5,
      validate: {
        min: 0
      }
    });

    // Add index for better performance
    await queryInterface.addIndex('products', ['minimum_quantity'], {
      name: 'products_minimum_quantity_index'
    });

    // Update existing products to have default minimum quantity
    await queryInterface.sequelize.query(
      'UPDATE products SET minimum_quantity = 5 WHERE minimum_quantity IS NULL'
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex('products', 'products_minimum_quantity_index');
    await queryInterface.removeColumn('products', 'minimum_quantity');
  }
};
