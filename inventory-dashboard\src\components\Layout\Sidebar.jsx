import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  Package,
  Truck,
  Warehouse,
  List,
  Receipt,
  ShoppingCart,
  ArrowLeftRight,
  PresentationChart,
  Users,
  Settings
} from 'lucide-react';

const Sidebar = () => {
  const location = useLocation();

  const menuItems = [
    {
      title: 'Products',
      icon: Package,
      path: '/products'
    },
    {
      title: 'Suppliers',
      icon: Truck,
      path: '/suppliers'
    },
    {
      title: 'Warehouses',
      icon: Warehouse,
      path: '/warehouses'
    },
    {
      title: 'Inventory',
      icon: List,
      path: '/inventory'
    },
    {
      title: 'Purchase Orders',
      icon: Receipt,
      path: '/purchase-orders'
    },
    {
      title: 'Sales Orders',
      icon: ShoppingCart,
      path: '/sales-orders'
    },
    {
      title: 'Transfers',
      icon: ArrowLeftRight,
      path: '/transfers'
    },
    {
      title: 'Reports',
      icon: PresentationChart,
      path: '/reports'
    },
    {
      title: 'Users & Roles',
      icon: Users,
      path: '/users'
    }
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <div className="w-80">
      <div className="flex h-full min-h-[700px] flex-col justify-between bg-[#121416] p-4">
        <div className="flex flex-col gap-4">
          <h1 className="text-white text-base font-medium leading-normal">WMS</h1>
          <div className="flex flex-col gap-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.path);

              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`flex items-center gap-3 px-3 py-2 rounded-full transition-colors duration-200 ${
                    active
                      ? 'bg-[#2c3035]'
                      : 'hover:bg-[#1a1d21]'
                  }`}
                >
                  <div className="text-white">
                    <Icon size={24} />
                  </div>
                  <p className="text-white text-sm font-medium leading-normal">{item.title}</p>
                </Link>
              );
            })}
          </div>
        </div>


        <div className="flex flex-col gap-1">
          <Link
            to="/settings"
            className={`flex items-center gap-3 px-3 py-2 rounded-full transition-colors duration-200 ${
              isActive('/settings')
                ? 'bg-[#2c3035]'
                : 'hover:bg-[#1a1d21]'
            }`}
          >
            <div className="text-white">
              <Settings size={24} />
            </div>
            <p className="text-white text-sm font-medium leading-normal">Settings</p>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
