import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  Package,
  Users,
  Warehouse,
  ClipboardList,
  TrendingUp,
  TrendingDown,
  ShoppingCart,
  DollarSign,
  BarChart3,
  Bell,
  Menu,
  X
} from 'lucide-react';

const Sidebar = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const location = useLocation();

  const menuItems = [
    {
      title: 'لوحة التحكم',
      icon: Home,
      path: '/dashboard',
      color: 'text-blue-600'
    },
    {
      title: 'المنتجات',
      icon: Package,
      path: '/products',
      color: 'text-green-600'
    },
    {
      title: 'الموردين',
      icon: Users,
      path: '/suppliers',
      color: 'text-purple-600'
    },
    {
      title: 'المستودعات',
      icon: Warehouse,
      path: '/warehouses',
      color: 'text-orange-600'
    },
    {
      title: 'الجرد',
      icon: ClipboardList,
      path: '/inventory',
      color: 'text-teal-600'
    },
    {
      title: 'حركة المخزون',
      icon: TrendingUp,
      path: '/movements',
      color: 'text-indigo-600'
    },
    {
      title: 'أوامر الشراء',
      icon: ShoppingCart,
      path: '/purchase-orders',
      color: 'text-red-600'
    },
    {
      title: 'أوامر البيع',
      icon: DollarSign,
      path: '/sales-orders',
      color: 'text-yellow-600'
    },
    {
      title: 'التقارير',
      icon: BarChart3,
      path: '/reports',
      color: 'text-pink-600'
    },
    {
      title: 'التنبيهات',
      icon: Bell,
      path: '/alerts',
      color: 'text-red-500'
    }
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <div className={`bg-white shadow-lg transition-all duration-300 ${
      isCollapsed ? 'w-16' : 'w-64'
    } min-h-screen flex flex-col`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 flex items-center justify-between">
        {!isCollapsed && (
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <Package className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900">إدارة المخزون</h1>
              <p className="text-xs text-gray-500">نظام متكامل</p>
            </div>
          </div>
        )}
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
        >
          {isCollapsed ? (
            <Menu className="w-5 h-5 text-gray-600" />
          ) : (
            <X className="w-5 h-5 text-gray-600" />
          )}
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const active = isActive(item.path);
            
            return (
              <li key={item.path}>
                <Link
                  to={item.path}
                  className={`flex items-center space-x-3 space-x-reverse p-3 rounded-lg transition-all duration-200 group ${
                    active
                      ? 'bg-primary-50 text-primary-700 border-r-4 border-primary-600'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-primary-600'
                  }`}
                >
                  <Icon className={`w-5 h-5 ${active ? 'text-primary-600' : item.color} group-hover:scale-110 transition-transform`} />
                  {!isCollapsed && (
                    <span className="font-medium">{item.title}</span>
                  )}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      {!isCollapsed && (
        <div className="p-4 border-t border-gray-200">
          <div className="text-center">
            <p className="text-xs text-gray-500">الإصدار 1.0.0</p>
            <p className="text-xs text-gray-400 mt-1">© 2024 جميع الحقوق محفوظة</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
