'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkInsert('sales_orders', [
      {
        order_number: 'SO-2024-001',
        customer_name: 'أحم<PERSON> محمد علي',
        customer_phone: '+966501234567',
        status: 'NEW',
        total_amount: 8500.00,
        order_date: new Date('2024-12-20T09:00:00'),
        delivery_date: new Date('2024-12-28T14:00:00'),
        notes: 'طلب عاجل للمعدات التقنية',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        order_number: 'SO-2024-002',
        customer_name: 'فاطمة أحمد السالم',
        customer_phone: '+966502345678',
        status: 'PARTIALLY_DELIVERED',
        total_amount: 4200.00,
        order_date: new Date('2024-12-22T10:30:00'),
        delivery_date: new Date('2024-12-30T10:00:00'),
        notes: 'طلب للمكتب الجديد',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        order_number: 'SO-2024-003',
        customer_name: 'خالد عبدالله المطيري',
        customer_phone: '+966503456789',
        status: 'COMPLETED',
        total_amount: 6750.00,
        order_date: new Date('2024-12-18T14:15:00'),
        delivery_date: new Date('2024-12-25T16:00:00'),
        notes: 'طلب للشركة الجديدة',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        order_number: 'SO-2024-004',
        customer_name: 'نورا سعد الغامدي',
        customer_phone: '+966504567890',
        status: 'NEW',
        total_amount: 3200.00,
        order_date: new Date('2024-12-25T11:20:00'),
        delivery_date: new Date('2025-01-02T09:00:00'),
        notes: 'طلب للمنزل الذكي',
        created_at: new Date(),
        updated_at: new Date()
      }
    ], {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('sales_orders', null, {});
  }
};
