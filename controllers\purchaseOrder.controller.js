const { PurchaseOrder, PurchaseOrderItem, Product, Supplier, Inventory, StockMovement, sequelize } = require('../models');
const { Op } = require('sequelize');

// Helper function for error response
const sendErrorResponse = (res, statusCode, message, error = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (error && process.env.NODE_ENV === 'development') {
    response.error = error.message;
  }
  
  return res.status(statusCode).json(response);
};

// Helper function for success response
const sendSuccessResponse = (res, statusCode, message, data = null) => {
  const response = {
    success: true,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (data !== null) {
    response.data = data;
  }
  
  return res.status(statusCode).json(response);
};

// Get all purchase orders
exports.getAllPurchaseOrders = async (req, res) => {
  try {
    const { page = 1, limit = 10, supplier_id, status, start_date, end_date } = req.query;
    const offset = (page - 1) * limit;
    
    // Build where clause
    const whereClause = {};
    
    if (supplier_id) {
      whereClause.supplier_id = supplier_id;
    }
    
    if (status) {
      whereClause.status = status;
    }
    
    if (start_date && end_date) {
      whereClause.order_date = {
        [Op.between]: [new Date(start_date), new Date(end_date)]
      };
    } else if (start_date) {
      whereClause.order_date = {
        [Op.gte]: new Date(start_date)
      };
    } else if (end_date) {
      whereClause.order_date = {
        [Op.lte]: new Date(end_date)
      };
    }
    
    const { count, rows } = await PurchaseOrder.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Supplier,
          as: 'supplier',
          attributes: ['id', 'name', 'contact_name', 'phone', 'email']
        },
        {
          model: PurchaseOrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'name', 'code', 'unit']
            }
          ]
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['order_date', 'DESC']]
    });
    
    const totalPages = Math.ceil(count / limit);
    
    sendSuccessResponse(res, 200, 'تم جلب أوامر الشراء بنجاح', {
      purchaseOrders: rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error in getAllPurchaseOrders:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب أوامر الشراء', error);
  }
};

// Get single purchase order by ID
exports.getPurchaseOrderById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const purchaseOrder = await PurchaseOrder.findByPk(id, {
      include: [
        {
          model: Supplier,
          as: 'supplier'
        },
        {
          model: PurchaseOrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product'
            }
          ]
        }
      ]
    });
    
    if (!purchaseOrder) {
      return sendErrorResponse(res, 404, 'أمر الشراء غير موجود');
    }
    
    // Calculate completion percentage
    const completionPercentage = await purchaseOrder.getCompletionPercentage();
    
    const response = {
      ...purchaseOrder.toJSON(),
      completion_percentage: completionPercentage
    };
    
    sendSuccessResponse(res, 200, 'تم جلب أمر الشراء بنجاح', { purchaseOrder: response });
  } catch (error) {
    console.error('Error in getPurchaseOrderById:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب أمر الشراء', error);
  }
};

// Create new purchase order
exports.createPurchaseOrder = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { supplier_id, delivery_date, notes, items } = req.body;
    
    // Validation
    if (!supplier_id || !items || !Array.isArray(items) || items.length === 0) {
      await transaction.rollback();
      return sendErrorResponse(res, 400, 'معرف المورد وعناصر الطلب مطلوبة');
    }
    
    // Check if supplier exists
    const supplier = await Supplier.findByPk(supplier_id);
    if (!supplier) {
      await transaction.rollback();
      return sendErrorResponse(res, 404, 'المورد غير موجود');
    }
    
    // Generate order number
    const orderNumber = await PurchaseOrder.generateOrderNumber();
    
    // Create purchase order
    const purchaseOrder = await PurchaseOrder.create({
      supplier_id,
      order_number: orderNumber,
      delivery_date: delivery_date ? new Date(delivery_date) : null,
      notes
    }, { transaction });
    
    // Validate and create items
    let totalAmount = 0;
    const createdItems = [];
    
    for (const item of items) {
      const { product_id, quantity_ordered, unit_price } = item;
      
      if (!product_id || !quantity_ordered || quantity_ordered < 1) {
        await transaction.rollback();
        return sendErrorResponse(res, 400, 'معرف المنتج والكمية المطلوبة مطلوبان لكل عنصر');
      }
      
      // Check if product exists
      const product = await Product.findByPk(product_id);
      if (!product) {
        await transaction.rollback();
        return sendErrorResponse(res, 404, `المنتج بالمعرف ${product_id} غير موجود`);
      }
      
      const orderItem = await PurchaseOrderItem.create({
        purchase_order_id: purchaseOrder.id,
        product_id,
        quantity_ordered,
        unit_price: unit_price || null
      }, { transaction });
      
      createdItems.push(orderItem);
      
      if (unit_price) {
        totalAmount += quantity_ordered * unit_price;
      }
    }
    
    // Update total amount
    await purchaseOrder.update({
      total_amount: totalAmount > 0 ? totalAmount : null
    }, { transaction });
    
    await transaction.commit();
    
    // Fetch the created purchase order with associations
    const createdPurchaseOrder = await PurchaseOrder.findByPk(purchaseOrder.id, {
      include: [
        {
          model: Supplier,
          as: 'supplier'
        },
        {
          model: PurchaseOrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product'
            }
          ]
        }
      ]
    });
    
    sendSuccessResponse(res, 201, 'تم إنشاء أمر الشراء بنجاح', { purchaseOrder: createdPurchaseOrder });
  } catch (error) {
    await transaction.rollback();
    console.error('Error in createPurchaseOrder:', error);
    
    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => err.message);
      return sendErrorResponse(res, 400, validationErrors.join(', '));
    }
    
    sendErrorResponse(res, 500, 'خطأ في إنشاء أمر الشراء', error);
  }
};

// Update purchase order
exports.updatePurchaseOrder = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { supplier_id, status, delivery_date, notes, total_amount } = req.body;

    const purchaseOrder = await PurchaseOrder.findByPk(id, { transaction });

    if (!purchaseOrder) {
      await transaction.rollback();
      return sendErrorResponse(res, 404, 'أمر الشراء غير موجود');
    }

    // Check if supplier exists (if provided)
    if (supplier_id) {
      const supplier = await Supplier.findByPk(supplier_id);
      if (!supplier) {
        await transaction.rollback();
        return sendErrorResponse(res, 404, 'المورد غير موجود');
      }
    }

    // Update purchase order
    await purchaseOrder.update({
      supplier_id: supplier_id || purchaseOrder.supplier_id,
      status: status || purchaseOrder.status,
      delivery_date: delivery_date ? new Date(delivery_date) : purchaseOrder.delivery_date,
      notes: notes !== undefined ? notes : purchaseOrder.notes,
      total_amount: total_amount !== undefined ? total_amount : purchaseOrder.total_amount
    }, { transaction });

    await transaction.commit();

    // Fetch updated purchase order with associations
    const updatedPurchaseOrder = await PurchaseOrder.findByPk(id, {
      include: [
        {
          model: Supplier,
          as: 'supplier'
        },
        {
          model: PurchaseOrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product'
            }
          ]
        }
      ]
    });

    sendSuccessResponse(res, 200, 'تم تحديث أمر الشراء بنجاح', { purchaseOrder: updatedPurchaseOrder });
  } catch (error) {
    await transaction.rollback();
    console.error('Error in updatePurchaseOrder:', error);

    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => err.message);
      return sendErrorResponse(res, 400, validationErrors.join(', '));
    }

    sendErrorResponse(res, 500, 'خطأ في تحديث أمر الشراء', error);
  }
};

// Delete purchase order
exports.deletePurchaseOrder = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    const purchaseOrder = await PurchaseOrder.findByPk(id, { transaction });

    if (!purchaseOrder) {
      await transaction.rollback();
      return sendErrorResponse(res, 404, 'أمر الشراء غير موجود');
    }

    // Check if order can be deleted (only NEW orders can be deleted)
    if (purchaseOrder.status !== 'NEW') {
      await transaction.rollback();
      return sendErrorResponse(res, 400, 'لا يمكن حذف أمر الشراء إلا إذا كان في حالة جديد');
    }

    await purchaseOrder.destroy({ transaction });
    await transaction.commit();

    sendSuccessResponse(res, 200, 'تم حذف أمر الشراء بنجاح');
  } catch (error) {
    await transaction.rollback();
    console.error('Error in deletePurchaseOrder:', error);
    sendErrorResponse(res, 500, 'خطأ في حذف أمر الشراء', error);
  }
};

// Receive products for purchase order
exports.receiveProducts = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { items, warehouse_id = 1 } = req.body; // Default to warehouse 1 if not specified

    if (!items || !Array.isArray(items) || items.length === 0) {
      await transaction.rollback();
      return sendErrorResponse(res, 400, 'عناصر الاستلام مطلوبة');
    }

    const purchaseOrder = await PurchaseOrder.findByPk(id, {
      include: [
        {
          model: PurchaseOrderItem,
          as: 'items'
        }
      ],
      transaction
    });

    if (!purchaseOrder) {
      await transaction.rollback();
      return sendErrorResponse(res, 404, 'أمر الشراء غير موجود');
    }

    if (purchaseOrder.status === 'COMPLETED' || purchaseOrder.status === 'CANCELLED') {
      await transaction.rollback();
      return sendErrorResponse(res, 400, 'لا يمكن استلام منتجات لأمر شراء مكتمل أو ملغي');
    }

    const receivedItems = [];

    for (const receivedItem of items) {
      const { product_id, quantity_received } = receivedItem;

      if (!product_id || !quantity_received || quantity_received < 1) {
        await transaction.rollback();
        return sendErrorResponse(res, 400, 'معرف المنتج والكمية المستلمة مطلوبان');
      }

      // Find the purchase order item
      const orderItem = await PurchaseOrderItem.findOne({
        where: {
          purchase_order_id: id,
          product_id: product_id
        },
        transaction
      });

      if (!orderItem) {
        await transaction.rollback();
        return sendErrorResponse(res, 404, `المنتج بالمعرف ${product_id} غير موجود في أمر الشراء`);
      }

      // Check if quantity doesn't exceed ordered quantity
      const newTotalReceived = orderItem.quantity_received + quantity_received;
      if (newTotalReceived > orderItem.quantity_ordered) {
        await transaction.rollback();
        return sendErrorResponse(res, 400, `الكمية المستلمة (${newTotalReceived}) تتجاوز الكمية المطلوبة (${orderItem.quantity_ordered}) للمنتج ${product_id}`);
      }

      // Update purchase order item
      await orderItem.update({
        quantity_received: newTotalReceived
      }, { transaction });

      // Create stock movement (IN)
      await StockMovement.create({
        product_id: product_id,
        warehouse_id: warehouse_id,
        type: 'IN',
        quantity: quantity_received,
        reference: purchaseOrder.order_number,
        description: `استلام من أمر الشراء ${purchaseOrder.order_number}`
      }, { transaction });

      // Update or create inventory record
      let [inventory, created] = await Inventory.findOrCreate({
        where: {
          product_id: product_id,
          warehouse_id: warehouse_id
        },
        defaults: {
          quantity: quantity_received,
          last_checked_at: new Date()
        },
        transaction
      });

      if (!created) {
        await inventory.update({
          quantity: inventory.quantity + quantity_received,
          last_checked_at: new Date()
        }, { transaction });
      }

      receivedItems.push({
        product_id,
        quantity_received,
        total_received: newTotalReceived,
        quantity_ordered: orderItem.quantity_ordered
      });
    }

    // Update purchase order status
    const allItems = await PurchaseOrderItem.findAll({
      where: { purchase_order_id: id },
      transaction
    });

    const allFullyReceived = allItems.every(item => item.quantity_received >= item.quantity_ordered);
    const anyPartiallyReceived = allItems.some(item => item.quantity_received > 0);

    let newStatus = purchaseOrder.status;
    if (allFullyReceived) {
      newStatus = 'COMPLETED';
    } else if (anyPartiallyReceived) {
      newStatus = 'PARTIALLY_DELIVERED';
    }

    await purchaseOrder.update({ status: newStatus }, { transaction });

    await transaction.commit();

    sendSuccessResponse(res, 200, 'تم استلام المنتجات بنجاح', {
      receivedItems,
      newStatus,
      message: `تم استلام ${receivedItems.length} منتج وتحديث المخزون`
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error in receiveProducts:', error);
    sendErrorResponse(res, 500, 'خطأ في استلام المنتجات', error);
  }
};

// Get purchase orders statistics
exports.getPurchaseOrdersStats = async (req, res) => {
  try {
    const totalOrders = await PurchaseOrder.count();
    const newOrders = await PurchaseOrder.count({
      where: { status: 'NEW' }
    });
    const partiallyDeliveredOrders = await PurchaseOrder.count({
      where: { status: 'PARTIALLY_DELIVERED' }
    });
    const completedOrders = await PurchaseOrder.count({
      where: { status: 'COMPLETED' }
    });
    const cancelledOrders = await PurchaseOrder.count({
      where: { status: 'CANCELLED' }
    });

    const totalAmount = await PurchaseOrder.sum('total_amount', {
      where: {
        total_amount: { [Op.ne]: null }
      }
    }) || 0;

    // Orders by supplier
    const ordersBySupplier = await PurchaseOrder.findAll({
      attributes: [
        'supplier_id',
        [PurchaseOrder.sequelize.fn('COUNT', PurchaseOrder.sequelize.col('id')), 'order_count'],
        [PurchaseOrder.sequelize.fn('SUM', PurchaseOrder.sequelize.col('total_amount')), 'total_value']
      ],
      include: [
        {
          model: Supplier,
          as: 'supplier',
          attributes: ['name', 'contact_name']
        }
      ],
      group: ['supplier_id', 'supplier.id'],
      order: [[PurchaseOrder.sequelize.fn('COUNT', PurchaseOrder.sequelize.col('id')), 'DESC']],
      limit: 10
    });

    // Recent orders
    const recentOrders = await PurchaseOrder.findAll({
      include: [
        {
          model: Supplier,
          as: 'supplier',
          attributes: ['name', 'contact_name']
        }
      ],
      order: [['order_date', 'DESC']],
      limit: 5
    });

    // Pending items (not fully received)
    const pendingItems = await PurchaseOrderItem.findAll({
      where: {
        quantity_received: {
          [Op.lt]: PurchaseOrder.sequelize.col('quantity_ordered')
        }
      },
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['name', 'code']
        },
        {
          model: PurchaseOrder,
          as: 'purchaseOrder',
          attributes: ['order_number', 'status'],
          include: [
            {
              model: Supplier,
              as: 'supplier',
              attributes: ['name']
            }
          ]
        }
      ],
      limit: 10
    });

    const stats = {
      totalOrders,
      ordersByStatus: {
        new: newOrders,
        partiallyDelivered: partiallyDeliveredOrders,
        completed: completedOrders,
        cancelled: cancelledOrders
      },
      totalAmount,
      ordersBySupplier,
      recentOrders,
      pendingItems
    };

    sendSuccessResponse(res, 200, 'تم جلب إحصائيات أوامر الشراء بنجاح', { stats });
  } catch (error) {
    console.error('Error in getPurchaseOrdersStats:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب إحصائيات أوامر الشراء', error);
  }
};
