const { Warehouse } = require('../models');
const { Op } = require('sequelize');

// Helper function for error response
const sendErrorResponse = (res, statusCode, message, error = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (error && process.env.NODE_ENV === 'development') {
    response.error = error.message;
  }
  
  return res.status(statusCode).json(response);
};

// Helper function for success response
const sendSuccessResponse = (res, statusCode, message, data = null) => {
  const response = {
    success: true,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (data !== null) {
    response.data = data;
  }
  
  return res.status(statusCode).json(response);
};

// Get all warehouses
exports.getAllWarehouses = async (req, res) => {
  try {
    const { page = 1, limit = 10, search, min_capacity } = req.query;
    const offset = (page - 1) * limit;
    
    // Build where clause
    const whereClause = {};
    
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { code: { [Op.iLike]: `%${search}%` } },
        { location: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }
    
    if (min_capacity) {
      whereClause.capacity = { [Op.gte]: parseInt(min_capacity) };
    }
    
    const { count, rows } = await Warehouse.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });
    
    const totalPages = Math.ceil(count / limit);
    
    sendSuccessResponse(res, 200, 'تم جلب المستودعات بنجاح', {
      warehouses: rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error in getAllWarehouses:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب المستودعات', error);
  }
};

// Get single warehouse by ID
exports.getWarehouseById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const warehouse = await Warehouse.findByPk(id);
    
    if (!warehouse) {
      return sendErrorResponse(res, 404, 'المستودع غير موجود');
    }
    
    sendSuccessResponse(res, 200, 'تم جلب المستودع بنجاح', { warehouse });
  } catch (error) {
    console.error('Error in getWarehouseById:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب المستودع', error);
  }
};

// Create new warehouse
exports.createWarehouse = async (req, res) => {
  try {
    const { name, code, location, capacity, description } = req.body;
    
    // Validation
    if (!name || !code) {
      return sendErrorResponse(res, 400, 'اسم المستودع والكود مطلوبان');
    }
    
    if (capacity !== undefined && capacity < 0) {
      return sendErrorResponse(res, 400, 'السعة يجب أن تكون أكبر من أو تساوي 0');
    }
    
    // Validate code format (alphanumeric only)
    if (!/^[a-zA-Z0-9]+$/.test(code)) {
      return sendErrorResponse(res, 400, 'كود المستودع يجب أن يحتوي على أحرف وأرقام فقط');
    }
    
    const warehouse = await Warehouse.create({
      name,
      code: code.toUpperCase(), // Convert to uppercase for consistency
      location,
      capacity,
      description
    });
    
    sendSuccessResponse(res, 201, 'تم إنشاء المستودع بنجاح', { warehouse });
  } catch (error) {
    console.error('Error in createWarehouse:', error);
    
    // Handle unique constraint error
    if (error.name === 'SequelizeUniqueConstraintError') {
      return sendErrorResponse(res, 400, 'كود المستودع موجود مسبقاً');
    }
    
    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => err.message);
      return sendErrorResponse(res, 400, validationErrors.join(', '));
    }
    
    sendErrorResponse(res, 500, 'خطأ في إنشاء المستودع', error);
  }
};

// Update warehouse
exports.updateWarehouse = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, location, capacity, description } = req.body;

    const warehouse = await Warehouse.findByPk(id);

    if (!warehouse) {
      return sendErrorResponse(res, 404, 'المستودع غير موجود');
    }

    // Validation
    if (capacity !== undefined && capacity < 0) {
      return sendErrorResponse(res, 400, 'السعة يجب أن تكون أكبر من أو تساوي 0');
    }

    // Validate code format if provided
    if (code && !/^[a-zA-Z0-9]+$/.test(code)) {
      return sendErrorResponse(res, 400, 'كود المستودع يجب أن يحتوي على أحرف وأرقام فقط');
    }

    // Update warehouse
    await warehouse.update({
      name: name || warehouse.name,
      code: code ? code.toUpperCase() : warehouse.code,
      location: location !== undefined ? location : warehouse.location,
      capacity: capacity !== undefined ? capacity : warehouse.capacity,
      description: description !== undefined ? description : warehouse.description
    });

    sendSuccessResponse(res, 200, 'تم تحديث المستودع بنجاح', { warehouse });
  } catch (error) {
    console.error('Error in updateWarehouse:', error);

    // Handle unique constraint error
    if (error.name === 'SequelizeUniqueConstraintError') {
      return sendErrorResponse(res, 400, 'كود المستودع موجود مسبقاً');
    }

    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => err.message);
      return sendErrorResponse(res, 400, validationErrors.join(', '));
    }

    sendErrorResponse(res, 500, 'خطأ في تحديث المستودع', error);
  }
};

// Delete warehouse
exports.deleteWarehouse = async (req, res) => {
  try {
    const { id } = req.params;

    const warehouse = await Warehouse.findByPk(id);

    if (!warehouse) {
      return sendErrorResponse(res, 404, 'المستودع غير موجود');
    }

    // Future: Check if warehouse has inventory before deletion
    // if (warehouse.inventory && warehouse.inventory.length > 0) {
    //   return sendErrorResponse(res, 400, 'لا يمكن حذف المستودع لأنه يحتوي على مخزون');
    // }

    await warehouse.destroy();

    sendSuccessResponse(res, 200, 'تم حذف المستودع بنجاح');
  } catch (error) {
    console.error('Error in deleteWarehouse:', error);
    sendErrorResponse(res, 500, 'خطأ في حذف المستودع', error);
  }
};

// Get warehouses statistics
exports.getWarehousesStats = async (req, res) => {
  try {
    const totalWarehouses = await Warehouse.count();
    const warehousesWithCapacity = await Warehouse.count({
      where: {
        capacity: { [Op.ne]: null }
      }
    });
    const warehousesWithoutCapacity = totalWarehouses - warehousesWithCapacity;
    const totalCapacity = await Warehouse.sum('capacity', {
      where: {
        capacity: { [Op.ne]: null }
      }
    });
    const averageCapacity = warehousesWithCapacity > 0 ? Math.round(totalCapacity / warehousesWithCapacity) : 0;

    const stats = {
      totalWarehouses,
      warehousesWithCapacity,
      warehousesWithoutCapacity,
      totalCapacity: totalCapacity || 0,
      averageCapacity
    };

    sendSuccessResponse(res, 200, 'تم جلب إحصائيات المستودعات بنجاح', { stats });
  } catch (error) {
    console.error('Error in getWarehousesStats:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب إحصائيات المستودعات', error);
  }
};
