{"info": {"name": "Suppliers Management API", "description": "مجموعة أمثلة لاختبار API إدارة الموردين", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Suppliers", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/suppliers", "host": ["{{baseUrl}}"], "path": ["api", "suppliers"]}}}, {"name": "Get Suppliers with Pagination", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/suppliers?page=1&limit=5", "host": ["{{baseUrl}}"], "path": ["api", "suppliers"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "5"}]}}}, {"name": "Search Suppliers", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/suppliers?search=التقنية", "host": ["{{baseUrl}}"], "path": ["api", "suppliers"], "query": [{"key": "search", "value": "التقنية"}]}}}, {"name": "Get Suppliers with Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/suppliers?include_products=true", "host": ["{{baseUrl}}"], "path": ["api", "suppliers"], "query": [{"key": "include_products", "value": "true"}]}}}, {"name": "Get Supplier by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/suppliers/1", "host": ["{{baseUrl}}"], "path": ["api", "suppliers", "1"]}}}, {"name": "Get Supplier with Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/suppliers/1?include_products=true", "host": ["{{baseUrl}}"], "path": ["api", "suppliers", "1"], "query": [{"key": "include_products", "value": "true"}]}}}, {"name": "Create Supplier - Tech Company", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"شركة التكنولوجيا المتطورة\",\n  \"contact_name\": \"أحمد محمد السعيد\",\n  \"phone\": \"966501234567\",\n  \"email\": \"<EMAIL>\",\n  \"tax_number\": \"TAX-NEW-001\",\n  \"address\": \"الرياض، حي الملك فهد، شارع الملك عبدالعزيز، مبنى رقم 456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/suppliers", "host": ["{{baseUrl}}"], "path": ["api", "suppliers"]}}}, {"name": "Create Supplier - Electronics Store", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"متجر الإلكترونيات الذكية\",\n  \"contact_name\": \"فاطمة أحمد الزهراني\",\n  \"phone\": \"966502345678\",\n  \"email\": \"<EMAIL>\",\n  \"tax_number\": \"TAX-NEW-002\",\n  \"address\": \"جدة، حي الروضة، طريق الملك عبدالله، مجمع الأعمال\"\n}"}, "url": {"raw": "{{baseUrl}}/api/suppliers", "host": ["{{baseUrl}}"], "path": ["api", "suppliers"]}}}, {"name": "Update Supplier", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"شركة التكنولوجيا المتطورة - محدث\",\n  \"phone\": \"966501234568\",\n  \"email\": \"<EMAIL>\",\n  \"address\": \"الرياض، حي الملك فهد، شارع الملك عبدالعزيز، مبنى رقم 456 - الطابق الثاني\"\n}"}, "url": {"raw": "{{baseUrl}}/api/suppliers/1", "host": ["{{baseUrl}}"], "path": ["api", "suppliers", "1"]}}}, {"name": "Delete Supplier", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/suppliers/1", "host": ["{{baseUrl}}"], "path": ["api", "suppliers", "1"]}}}, {"name": "Get Suppliers Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/suppliers/stats", "host": ["{{baseUrl}}"], "path": ["api", "suppliers", "stats"]}}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}