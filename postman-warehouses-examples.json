{"info": {"name": "Warehouses Management API", "description": "مجموعة أمثلة لاختبار API إدارة المستودعات", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Warehouses", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/warehouses", "host": ["{{baseUrl}}"], "path": ["api", "warehouses"]}}}, {"name": "Get Warehouses with Pagination", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/warehouses?page=1&limit=3", "host": ["{{baseUrl}}"], "path": ["api", "warehouses"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "3"}]}}}, {"name": "Search Warehouses", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/warehouses?search=الرياض", "host": ["{{baseUrl}}"], "path": ["api", "warehouses"], "query": [{"key": "search", "value": "الرياض"}]}}}, {"name": "Get Warehouses with Min Capacity", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/warehouses?min_capacity=5000", "host": ["{{baseUrl}}"], "path": ["api", "warehouses"], "query": [{"key": "min_capacity", "value": "5000"}]}}}, {"name": "Get Warehouse by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/warehouses/1", "host": ["{{baseUrl}}"], "path": ["api", "warehouses", "1"]}}}, {"name": "Create Warehouse - Main Storage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"مستودع التخزين الرئيسي\",\n  \"code\": \"WH-MAIN-001\",\n  \"location\": \"الرياض، المنطقة الصناعية الأولى\",\n  \"capacity\": 15000,\n  \"description\": \"مستودع رئيسي كبير للتخزين العام مع مرافق متطورة\"\n}"}, "url": {"raw": "{{baseUrl}}/api/warehouses", "host": ["{{baseUrl}}"], "path": ["api", "warehouses"]}}}, {"name": "Create Warehouse - Branch Storage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"مستودع الفرع الشرقي\",\n  \"code\": \"WH-EAST-001\",\n  \"location\": \"الخبر، حي الراكة الشمالية\",\n  \"capacity\": 8000,\n  \"description\": \"مستودع فرعي يخدم المنطقة الشرقية\"\n}"}, "url": {"raw": "{{baseUrl}}/api/warehouses", "host": ["{{baseUrl}}"], "path": ["api", "warehouses"]}}}, {"name": "Create Warehouse - No Capacity Limit", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"مستودع مفتوح السعة\",\n  \"code\": \"WH-OPEN-001\",\n  \"location\": \"أبها، المنطقة الصناعية\",\n  \"description\": \"مستودع بدون حد أقصى للسعة\"\n}"}, "url": {"raw": "{{baseUrl}}/api/warehouses", "host": ["{{baseUrl}}"], "path": ["api", "warehouses"]}}}, {"name": "Update Warehouse", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"المستودع الرئيسي - الرياض المحدث\",\n  \"capacity\": 12000,\n  \"description\": \"المستودع الرئيسي في الرياض بعد التوسعة والتحديث\"\n}"}, "url": {"raw": "{{baseUrl}}/api/warehouses/1", "host": ["{{baseUrl}}"], "path": ["api", "warehouses", "1"]}}}, {"name": "Delete Warehouse", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/warehouses/1", "host": ["{{baseUrl}}"], "path": ["api", "warehouses", "1"]}}}, {"name": "Get Warehouses Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/warehouses/stats", "host": ["{{baseUrl}}"], "path": ["api", "warehouses", "stats"]}}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}