'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class SalesOrder extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Define associations here
      SalesOrder.hasMany(models.SalesOrderItem, {
        foreignKey: 'sales_order_id',
        as: 'items'
      });
    }

    // Instance method to check if order is new
    isNew() {
      return this.status === 'NEW';
    }

    // Instance method to check if order is completed
    isCompleted() {
      return this.status === 'COMPLETED';
    }

    // Instance method to check if order is cancelled
    isCancelled() {
      return this.status === 'CANCELLED';
    }

    // Instance method to check if order is partially delivered
    isPartiallyDelivered() {
      return this.status === 'PARTIALLY_DELIVERED';
    }

    // Instance method to get order summary
    getOrderSummary() {
      return {
        id: this.id,
        order_number: this.order_number,
        customer_name: this.customer_name,
        customer_phone: this.customer_phone,
        status: this.status,
        total_amount: this.total_amount,
        order_date: this.order_date,
        delivery_date: this.delivery_date
      };
    }

    // Static method to generate order number
    static async generateOrderNumber() {
      const year = new Date().getFullYear();
      const count = await this.count({
        where: {
          order_number: {
            [sequelize.Sequelize.Op.like]: `SO-${year}-%`
          }
        }
      });
      return `SO-${year}-${String(count + 1).padStart(3, '0')}`;
    }

    // Static method to find orders by customer
    static async findByCustomer(customerName) {
      return await this.findAll({
        where: {
          customer_name: {
            [sequelize.Sequelize.Op.iLike]: `%${customerName}%`
          }
        },
        include: [
          {
            model: sequelize.models.SalesOrderItem,
            as: 'items',
            include: [
              {
                model: sequelize.models.Product,
                as: 'product'
              }
            ]
          }
        ],
        order: [['order_date', 'DESC']]
      });
    }

    // Static method to find orders by status
    static async findByStatus(status) {
      return await this.findAll({
        where: {
          status: status
        },
        include: [
          {
            model: sequelize.models.SalesOrderItem,
            as: 'items',
            include: [
              {
                model: sequelize.models.Product,
                as: 'product'
              }
            ]
          }
        ],
        order: [['order_date', 'DESC']]
      });
    }

    // Instance method to calculate completion percentage
    async getCompletionPercentage() {
      const items = await this.getItems();
      if (!items || items.length === 0) return 0;
      
      let totalOrdered = 0;
      let totalDelivered = 0;
      
      items.forEach(item => {
        totalOrdered += item.quantity_ordered;
        totalDelivered += item.quantity_delivered;
      });
      
      return totalOrdered > 0 ? Math.round((totalDelivered / totalOrdered) * 100) : 0;
    }

    // Static method to get orders within date range
    static async findByDateRange(startDate, endDate) {
      return await this.findAll({
        where: {
          order_date: {
            [sequelize.Sequelize.Op.between]: [startDate, endDate]
          }
        },
        include: [
          {
            model: sequelize.models.SalesOrderItem,
            as: 'items',
            include: [
              {
                model: sequelize.models.Product,
                as: 'product'
              }
            ]
          }
        ],
        order: [['order_date', 'DESC']]
      });
    }
  }

  SalesOrder.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    order_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: {
        msg: 'رقم الطلب يجب أن يكون فريد'
      },
      validate: {
        notEmpty: {
          msg: 'رقم الطلب مطلوب'
        },
        len: {
          args: [5, 50],
          msg: 'رقم الطلب يجب أن يكون بين 5 و 50 حرف'
        }
      }
    },
    customer_name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'اسم العميل مطلوب'
        },
        len: {
          args: [2, 100],
          msg: 'اسم العميل يجب أن يكون بين 2 و 100 حرف'
        }
      }
    },
    customer_phone: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [10, 20],
          msg: 'رقم الهاتف يجب أن يكون بين 10 و 20 رقم'
        }
      }
    },
    status: {
      type: DataTypes.ENUM('NEW', 'PARTIALLY_DELIVERED', 'COMPLETED', 'CANCELLED'),
      allowNull: false,
      defaultValue: 'NEW',
      validate: {
        isIn: {
          args: [['NEW', 'PARTIALLY_DELIVERED', 'COMPLETED', 'CANCELLED']],
          msg: 'حالة الطلب يجب أن تكون NEW أو PARTIALLY_DELIVERED أو COMPLETED أو CANCELLED'
        }
      }
    },
    total_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      validate: {
        min: {
          args: [0],
          msg: 'المبلغ الإجمالي يجب أن يكون أكبر من أو يساوي 0'
        }
      }
    },
    order_date: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    delivery_date: {
      type: DataTypes.DATE,
      allowNull: true
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'SalesOrder',
    tableName: 'sales_orders',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['order_number']
      },
      {
        fields: ['customer_name']
      },
      {
        fields: ['customer_phone']
      },
      {
        fields: ['status']
      },
      {
        fields: ['order_date']
      },
      {
        fields: ['delivery_date']
      }
    ]
  });

  return SalesOrder;
};
