const { Supplier, Product } = require('../models');
const { Op } = require('sequelize');

// Helper function for error response
const sendErrorResponse = (res, statusCode, message, error = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (error && process.env.NODE_ENV === 'development') {
    response.error = error.message;
  }
  
  return res.status(statusCode).json(response);
};

// Helper function for success response
const sendSuccessResponse = (res, statusCode, message, data = null) => {
  const response = {
    success: true,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (data !== null) {
    response.data = data;
  }
  
  return res.status(statusCode).json(response);
};

// Get all suppliers
exports.getAllSuppliers = async (req, res) => {
  try {
    const { page = 1, limit = 10, search, include_products } = req.query;
    const offset = (page - 1) * limit;
    
    // Build where clause
    const whereClause = {};
    
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { contact_name: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
        { tax_number: { [Op.iLike]: `%${search}%` } }
      ];
    }
    
    // Build include clause
    const includeClause = [];
    if (include_products === 'true') {
      includeClause.push({
        model: Product,
        as: 'products',
        attributes: ['id', 'name', 'code', 'quantity']
      });
    }
    
    const { count, rows } = await Supplier.findAndCountAll({
      where: whereClause,
      include: includeClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });
    
    const totalPages = Math.ceil(count / limit);
    
    sendSuccessResponse(res, 200, 'تم جلب الموردين بنجاح', {
      suppliers: rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error in getAllSuppliers:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب الموردين', error);
  }
};

// Get single supplier by ID
exports.getSupplierById = async (req, res) => {
  try {
    const { id } = req.params;
    const { include_products } = req.query;
    
    // Build include clause
    const includeClause = [];
    if (include_products === 'true') {
      includeClause.push({
        model: Product,
        as: 'products'
      });
    }
    
    const supplier = await Supplier.findByPk(id, {
      include: includeClause
    });
    
    if (!supplier) {
      return sendErrorResponse(res, 404, 'المورد غير موجود');
    }
    
    sendSuccessResponse(res, 200, 'تم جلب المورد بنجاح', { supplier });
  } catch (error) {
    console.error('Error in getSupplierById:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب المورد', error);
  }
};

// Create new supplier
exports.createSupplier = async (req, res) => {
  try {
    const { name, contact_name, phone, email, tax_number, address } = req.body;
    
    // Validation
    if (!name) {
      return sendErrorResponse(res, 400, 'اسم المورد مطلوب');
    }
    
    // Validate email format if provided
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return sendErrorResponse(res, 400, 'البريد الإلكتروني يجب أن يكون بصيغة صحيحة');
    }
    
    // Validate phone format if provided
    if (phone && !/^\d{7,20}$/.test(phone.replace(/[\s\-\(\)]/g, ''))) {
      return sendErrorResponse(res, 400, 'رقم الهاتف يجب أن يحتوي على أرقام فقط (7-20 رقم)');
    }
    
    const supplier = await Supplier.create({
      name,
      contact_name,
      phone: phone ? phone.replace(/[\s\-\(\)]/g, '') : null,
      email,
      tax_number,
      address
    });
    
    sendSuccessResponse(res, 201, 'تم إنشاء المورد بنجاح', { supplier });
  } catch (error) {
    console.error('Error in createSupplier:', error);
    
    // Handle unique constraint error
    if (error.name === 'SequelizeUniqueConstraintError') {
      return sendErrorResponse(res, 400, 'الرقم الضريبي موجود مسبقاً');
    }
    
    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => err.message);
      return sendErrorResponse(res, 400, validationErrors.join(', '));
    }
    
    sendErrorResponse(res, 500, 'خطأ في إنشاء المورد', error);
  }
};

// Update supplier
exports.updateSupplier = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, contact_name, phone, email, tax_number, address } = req.body;

    const supplier = await Supplier.findByPk(id);

    if (!supplier) {
      return sendErrorResponse(res, 404, 'المورد غير موجود');
    }

    // Validate email format if provided
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return sendErrorResponse(res, 400, 'البريد الإلكتروني يجب أن يكون بصيغة صحيحة');
    }

    // Validate phone format if provided
    if (phone && !/^\d{7,20}$/.test(phone.replace(/[\s\-\(\)]/g, ''))) {
      return sendErrorResponse(res, 400, 'رقم الهاتف يجب أن يحتوي على أرقام فقط (7-20 رقم)');
    }

    // Update supplier
    await supplier.update({
      name: name || supplier.name,
      contact_name: contact_name !== undefined ? contact_name : supplier.contact_name,
      phone: phone ? phone.replace(/[\s\-\(\)]/g, '') : (phone !== undefined ? phone : supplier.phone),
      email: email !== undefined ? email : supplier.email,
      tax_number: tax_number !== undefined ? tax_number : supplier.tax_number,
      address: address !== undefined ? address : supplier.address
    });

    sendSuccessResponse(res, 200, 'تم تحديث المورد بنجاح', { supplier });
  } catch (error) {
    console.error('Error in updateSupplier:', error);

    // Handle unique constraint error
    if (error.name === 'SequelizeUniqueConstraintError') {
      return sendErrorResponse(res, 400, 'الرقم الضريبي موجود مسبقاً');
    }

    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => err.message);
      return sendErrorResponse(res, 400, validationErrors.join(', '));
    }

    sendErrorResponse(res, 500, 'خطأ في تحديث المورد', error);
  }
};

// Delete supplier
exports.deleteSupplier = async (req, res) => {
  try {
    const { id } = req.params;

    const supplier = await Supplier.findByPk(id, {
      include: [{
        model: Product,
        as: 'products'
      }]
    });

    if (!supplier) {
      return sendErrorResponse(res, 404, 'المورد غير موجود');
    }

    // Check if supplier has products
    if (supplier.products && supplier.products.length > 0) {
      return sendErrorResponse(res, 400, 'لا يمكن حذف المورد لأنه مرتبط بمنتجات موجودة');
    }

    await supplier.destroy();

    sendSuccessResponse(res, 200, 'تم حذف المورد بنجاح');
  } catch (error) {
    console.error('Error in deleteSupplier:', error);
    sendErrorResponse(res, 500, 'خطأ في حذف المورد', error);
  }
};

// Get suppliers statistics
exports.getSuppliersStats = async (req, res) => {
  try {
    const totalSuppliers = await Supplier.count();
    const suppliersWithProducts = await Supplier.count({
      include: [{
        model: Product,
        as: 'products',
        required: true
      }]
    });
    const suppliersWithoutProducts = totalSuppliers - suppliersWithProducts;
    const suppliersWithEmail = await Supplier.count({
      where: {
        email: { [Op.ne]: null }
      }
    });
    const suppliersWithPhone = await Supplier.count({
      where: {
        phone: { [Op.ne]: null }
      }
    });

    const stats = {
      totalSuppliers,
      suppliersWithProducts,
      suppliersWithoutProducts,
      suppliersWithEmail,
      suppliersWithPhone
    };

    sendSuccessResponse(res, 200, 'تم جلب إحصائيات الموردين بنجاح', { stats });
  } catch (error) {
    console.error('Error in getSuppliersStats:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب إحصائيات الموردين', error);
  }
};
