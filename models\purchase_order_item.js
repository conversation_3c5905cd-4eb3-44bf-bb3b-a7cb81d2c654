'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class PurchaseOrderItem extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Define associations here
      PurchaseOrderItem.belongsTo(models.PurchaseOrder, {
        foreignKey: 'purchase_order_id',
        as: 'purchaseOrder'
      });
      
      PurchaseOrderItem.belongsTo(models.Product, {
        foreignKey: 'product_id',
        as: 'product'
      });
    }

    // Instance method to check if item is fully received
    isFullyReceived() {
      return this.quantity_received >= this.quantity_ordered;
    }

    // Instance method to check if item is partially received
    isPartiallyReceived() {
      return this.quantity_received > 0 && this.quantity_received < this.quantity_ordered;
    }

    // Instance method to get remaining quantity
    getRemainingQuantity() {
      return Math.max(0, this.quantity_ordered - this.quantity_received);
    }

    // Instance method to get completion percentage
    getCompletionPercentage() {
      return this.quantity_ordered > 0 
        ? Math.round((this.quantity_received / this.quantity_ordered) * 100) 
        : 0;
    }

    // Instance method to get total value
    getTotalValue() {
      return this.unit_price ? this.quantity_ordered * this.unit_price : 0;
    }

    // Instance method to get received value
    getReceivedValue() {
      return this.unit_price ? this.quantity_received * this.unit_price : 0;
    }

    // Instance method to get item summary
    getItemSummary() {
      return {
        id: this.id,
        product_id: this.product_id,
        quantity_ordered: this.quantity_ordered,
        quantity_received: this.quantity_received,
        remaining_quantity: this.getRemainingQuantity(),
        completion_percentage: this.getCompletionPercentage(),
        unit_price: this.unit_price,
        total_value: this.getTotalValue(),
        received_value: this.getReceivedValue()
      };
    }

    // Static method to find items by purchase order
    static async findByPurchaseOrder(purchaseOrderId) {
      return await this.findAll({
        where: {
          purchase_order_id: purchaseOrderId
        },
        include: [
          {
            model: sequelize.models.Product,
            as: 'product'
          }
        ],
        order: [['created_at', 'ASC']]
      });
    }

    // Static method to find items by product
    static async findByProduct(productId) {
      return await this.findAll({
        where: {
          product_id: productId
        },
        include: [
          {
            model: sequelize.models.PurchaseOrder,
            as: 'purchaseOrder',
            include: [
              {
                model: sequelize.models.Supplier,
                as: 'supplier'
              }
            ]
          }
        ],
        order: [['created_at', 'DESC']]
      });
    }

    // Static method to get pending items (not fully received)
    static async getPendingItems() {
      return await this.findAll({
        where: {
          quantity_received: {
            [sequelize.Sequelize.Op.lt]: sequelize.Sequelize.col('quantity_ordered')
          }
        },
        include: [
          {
            model: sequelize.models.Product,
            as: 'product'
          },
          {
            model: sequelize.models.PurchaseOrder,
            as: 'purchaseOrder',
            include: [
              {
                model: sequelize.models.Supplier,
                as: 'supplier'
              }
            ]
          }
        ],
        order: [['created_at', 'ASC']]
      });
    }
  }

  PurchaseOrderItem.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    purchase_order_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'purchase_orders',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'معرف أمر الشراء مطلوب'
        },
        isInt: {
          msg: 'معرف أمر الشراء يجب أن يكون رقم صحيح'
        }
      }
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'products',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'معرف المنتج مطلوب'
        },
        isInt: {
          msg: 'معرف المنتج يجب أن يكون رقم صحيح'
        }
      }
    },
    quantity_ordered: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        notNull: {
          msg: 'الكمية المطلوبة مطلوبة'
        },
        min: {
          args: [1],
          msg: 'الكمية المطلوبة يجب أن تكون أكبر من أو تساوي 1'
        },
        isInt: {
          msg: 'الكمية المطلوبة يجب أن تكون رقم صحيح'
        }
      }
    },
    quantity_received: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: {
          args: [0],
          msg: 'الكمية المستلمة يجب أن تكون أكبر من أو تساوي 0'
        },
        isInt: {
          msg: 'الكمية المستلمة يجب أن تكون رقم صحيح'
        }
      }
    },
    unit_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      validate: {
        min: {
          args: [0],
          msg: 'سعر الوحدة يجب أن يكون أكبر من أو يساوي 0'
        }
      }
    }
  }, {
    sequelize,
    modelName: 'PurchaseOrderItem',
    tableName: 'purchase_order_items',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['purchase_order_id']
      },
      {
        fields: ['product_id']
      },
      {
        fields: ['quantity_ordered']
      },
      {
        fields: ['quantity_received']
      },
      {
        unique: true,
        fields: ['purchase_order_id', 'product_id']
      }
    ]
  });

  return PurchaseOrderItem;
};
