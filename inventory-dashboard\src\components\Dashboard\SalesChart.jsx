import { Line<PERSON>hart, Line, <PERSON>Axis, <PERSON>A<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const SalesChart = ({ data = [] }) => {
  // Sample data if no data provided
  const sampleData = [
    { month: 'يناير', sales: 4000, orders: 24 },
    { month: 'فبراير', sales: 3000, orders: 18 },
    { month: 'مارس', sales: 5000, orders: 32 },
    { month: 'أبريل', sales: 4500, orders: 28 },
    { month: 'مايو', sales: 6000, orders: 38 },
    { month: 'يونيو', sales: 5500, orders: 35 },
  ];

  const chartData = data.length > 0 ? data : sampleData;

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name === 'sales' ? 'المبيعات' : 'الطلبات'}: {entry.value.toLocaleString()}
              {entry.name === 'sales' ? ' ريال' : ' طلب'}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">اتجاه المبيعات</h3>
        <div className="flex items-center space-x-4 space-x-reverse text-sm">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 rounded-full ml-2"></div>
            <span className="text-gray-600">المبيعات</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full ml-2"></div>
            <span className="text-gray-600">الطلبات</span>
          </div>
        </div>
      </div>
      
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="month" 
              stroke="#6b7280"
              fontSize={12}
            />
            <YAxis 
              stroke="#6b7280"
              fontSize={12}
            />
            <Tooltip content={<CustomTooltip />} />
            <Line 
              type="monotone" 
              dataKey="sales" 
              stroke="#3b82f6" 
              strokeWidth={3}
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
            />
            <Line 
              type="monotone" 
              dataKey="orders" 
              stroke="#10b981" 
              strokeWidth={3}
              dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default SalesChart;
