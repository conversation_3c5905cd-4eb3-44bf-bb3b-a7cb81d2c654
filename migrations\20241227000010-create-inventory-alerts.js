'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('inventory_alerts', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      product_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'products',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      warehouse_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'warehouses',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      type: {
        type: Sequelize.ENUM('LOW_STOCK', 'OUT_OF_STOCK'),
        allowNull: false
      },
      level: {
        type: Sequelize.ENUM('CRITICAL', 'HIGH', 'MEDIUM'),
        allowNull: false
      },
      quantity: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      minimum_quantity: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      alert_message: {
        type: Sequelize.STRING,
        allowNull: false
      },
      status: {
        type: Sequelize.ENUM('NEW', 'SEEN', 'RESOLVED'),
        allowNull: false,
        defaultValue: 'NEW'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes for better performance
    await queryInterface.addIndex('inventory_alerts', ['product_id'], {
      name: 'inventory_alerts_product_id_index'
    });

    await queryInterface.addIndex('inventory_alerts', ['warehouse_id'], {
      name: 'inventory_alerts_warehouse_id_index'
    });

    await queryInterface.addIndex('inventory_alerts', ['type'], {
      name: 'inventory_alerts_type_index'
    });

    await queryInterface.addIndex('inventory_alerts', ['level'], {
      name: 'inventory_alerts_level_index'
    });

    await queryInterface.addIndex('inventory_alerts', ['status'], {
      name: 'inventory_alerts_status_index'
    });

    await queryInterface.addIndex('inventory_alerts', ['created_at'], {
      name: 'inventory_alerts_created_at_index'
    });

    // Composite index for active alerts
    await queryInterface.addIndex('inventory_alerts', ['product_id', 'warehouse_id', 'status'], {
      name: 'inventory_alerts_active_index'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('inventory_alerts');
  }
};
