const express = require('express');
const router = express.Router();
const reportController = require('../controllers/report.controller');

// Middleware for request logging
const requestLogger = (req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.originalUrl}`);
  next();
};

// Apply logging middleware to all routes
router.use(requestLogger);

// Validation middleware for query parameters
const validateQueryParams = (req, res, next) => {
  const { limit } = req.query;
  
  // Validate limit parameter if provided
  if (limit && (isNaN(parseInt(limit)) || parseInt(limit) < 1 || parseInt(limit) > 100)) {
    return res.status(400).json({
      success: false,
      message: 'معامل الحد الأقصى (limit) يجب أن يكون رقم بين 1 و 100',
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

// Routes

/**
 * @route   GET /api/reports/kpis
 * @desc    Get main KPIs (Key Performance Indicators)
 * @access  Public
 * @returns {Object} Main dashboard KPIs including inventory, orders, financial data
 */
router.get('/kpis', reportController.getKPIs);

/**
 * @route   GET /api/reports/sales-trend
 * @desc    Get sales trend for the last 6 months
 * @access  Public
 * @returns {Array} Monthly sales data with revenue and order counts
 */
router.get('/sales-trend', reportController.getSalesTrend);

/**
 * @route   GET /api/reports/top-products
 * @desc    Get top selling products
 * @access  Public
 * @params  ?limit=5 (default: 5, max: 100)
 * @returns {Array} Top selling products with quantities and revenue
 */
router.get('/top-products', validateQueryParams, reportController.getTopProducts);

/**
 * @route   GET /api/reports/top-suppliers
 * @desc    Get top suppliers by purchase value
 * @access  Public
 * @params  ?limit=5 (default: 5, max: 100)
 * @returns {Array} Top suppliers with order counts and total values
 */
router.get('/top-suppliers', validateQueryParams, reportController.getTopSuppliers);

/**
 * @route   GET /api/reports/inventory-alerts
 * @desc    Get inventory alerts (low stock and out of stock products)
 * @access  Public
 * @returns {Object} Low stock and out of stock products with alert levels
 */
router.get('/inventory-alerts', reportController.getInventoryAlerts);

/**
 * @route   GET /api/reports/orders-status
 * @desc    Get orders status distribution for purchase and sales orders
 * @access  Public
 * @returns {Object} Status distribution with counts and percentages
 */
router.get('/orders-status', reportController.getOrdersStatus);

/**
 * @route   GET /api/reports/dashboard
 * @desc    Get comprehensive dashboard data (combines multiple reports)
 * @access  Public
 * @returns {Object} Complete dashboard data for frontend
 */
router.get('/dashboard', async (req, res) => {
  try {
    // This endpoint combines multiple reports for a complete dashboard view
    const kpisResponse = await new Promise((resolve, reject) => {
      const mockRes = {
        status: () => mockRes,
        json: (data) => resolve(data)
      };
      reportController.getKPIs({ query: {} }, mockRes);
    });
    
    const salesTrendResponse = await new Promise((resolve, reject) => {
      const mockRes = {
        status: () => mockRes,
        json: (data) => resolve(data)
      };
      reportController.getSalesTrend({ query: {} }, mockRes);
    });
    
    const topProductsResponse = await new Promise((resolve, reject) => {
      const mockRes = {
        status: () => mockRes,
        json: (data) => resolve(data)
      };
      reportController.getTopProducts({ query: { limit: 5 } }, mockRes);
    });
    
    const inventoryAlertsResponse = await new Promise((resolve, reject) => {
      const mockRes = {
        status: () => mockRes,
        json: (data) => resolve(data)
      };
      reportController.getInventoryAlerts({ query: {} }, mockRes);
    });
    
    const ordersStatusResponse = await new Promise((resolve, reject) => {
      const mockRes = {
        status: () => mockRes,
        json: (data) => resolve(data)
      };
      reportController.getOrdersStatus({ query: {} }, mockRes);
    });
    
    const dashboardData = {
      success: true,
      message: 'تم جلب بيانات لوحة التحكم بنجاح',
      timestamp: new Date().toISOString(),
      data: {
        kpis: kpisResponse.data?.kpis || {},
        sales_trend: salesTrendResponse.data?.trend || [],
        top_products: topProductsResponse.data?.products || [],
        inventory_alerts: inventoryAlertsResponse.data?.alerts || {},
        orders_status: ordersStatusResponse.data?.orders_status || {}
      }
    };
    
    res.status(200).json(dashboardData);
  } catch (error) {
    console.error('Error in dashboard endpoint:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب بيانات لوحة التحكم',
      timestamp: new Date().toISOString(),
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
