const { 
  Product, 
  Supplier, 
  Warehouse, 
  Inventory, 
  StockMovement, 
  PurchaseOrder, 
  PurchaseOrderItem,
  SalesOrder, 
  SalesOrderItem,
  sequelize 
} = require('../models');
const { Op } = require('sequelize');

// Helper function for error response
const sendErrorResponse = (res, statusCode, message, error = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (error && process.env.NODE_ENV === 'development') {
    response.error = error.message;
  }
  
  return res.status(statusCode).json(response);
};

// Helper function for success response
const sendSuccessResponse = (res, statusCode, message, data = null) => {
  const response = {
    success: true,
    message,
    timestamp: new Date().toISOString()
  };
  
  if (data !== null) {
    response.data = data;
  }
  
  return res.status(statusCode).json(response);
};

// Get main KPIs
exports.getKPIs = async (req, res) => {
  try {
    // Basic counts
    const totalProducts = await Product.count();
    const totalSuppliers = await Supplier.count();
    const totalWarehouses = await Warehouse.count();
    
    // Orders counts
    const totalPurchaseOrders = await PurchaseOrder.count();
    const totalSalesOrders = await SalesOrder.count();
    
    // Stock movements counts
    const totalStockMovements = await StockMovement.count();
    const inMovements = await StockMovement.count({
      where: { type: 'IN' }
    });
    const outMovements = await StockMovement.count({
      where: { type: 'OUT' }
    });
    
    // Financial totals
    const totalSalesValue = await SalesOrder.sum('total_amount', {
      where: {
        total_amount: { [Op.ne]: null },
        status: { [Op.ne]: 'CANCELLED' }
      }
    }) || 0;
    
    const totalPurchasesValue = await PurchaseOrder.sum('total_amount', {
      where: {
        total_amount: { [Op.ne]: null },
        status: { [Op.ne]: 'CANCELLED' }
      }
    }) || 0;
    
    // Low stock products count
    const lowStockProducts = await Inventory.count({
      where: {
        [Op.and]: [
          { minimum_quantity: { [Op.ne]: null } },
          { quantity: { [Op.lte]: { [Op.col]: 'minimum_quantity' } } }
        ]
      }
    });
    
    // Out of stock products count
    const outOfStockProducts = await Inventory.count({
      where: { quantity: 0 }
    });
    
    // Total inventory value (estimated)
    const totalInventoryQuantity = await Inventory.sum('quantity') || 0;
    
    // Active orders (not completed or cancelled)
    const activePurchaseOrders = await PurchaseOrder.count({
      where: {
        status: { [Op.in]: ['NEW', 'PARTIALLY_DELIVERED'] }
      }
    });
    
    const activeSalesOrders = await SalesOrder.count({
      where: {
        status: { [Op.in]: ['NEW', 'PARTIALLY_DELIVERED'] }
      }
    });
    
    const kpis = {
      inventory: {
        totalProducts,
        totalInventoryQuantity,
        lowStockProducts,
        outOfStockProducts,
        totalWarehouses
      },
      suppliers: {
        totalSuppliers
      },
      orders: {
        purchase: {
          total: totalPurchaseOrders,
          active: activePurchaseOrders,
          totalValue: parseFloat(totalPurchasesValue)
        },
        sales: {
          total: totalSalesOrders,
          active: activeSalesOrders,
          totalValue: parseFloat(totalSalesValue)
        }
      },
      movements: {
        total: totalStockMovements,
        inbound: inMovements,
        outbound: outMovements
      },
      financial: {
        totalRevenue: parseFloat(totalSalesValue),
        totalPurchases: parseFloat(totalPurchasesValue),
        grossProfit: parseFloat(totalSalesValue - totalPurchasesValue)
      }
    };
    
    sendSuccessResponse(res, 200, 'تم جلب المؤشرات الرئيسية بنجاح', { kpis });
  } catch (error) {
    console.error('Error in getKPIs:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب المؤشرات الرئيسية', error);
  }
};

// Get sales trend (last 6 months)
exports.getSalesTrend = async (req, res) => {
  try {
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    
    const salesTrend = await SalesOrder.findAll({
      attributes: [
        [sequelize.fn('DATE_TRUNC', 'month', sequelize.col('order_date')), 'month'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'orders_count'],
        [sequelize.fn('SUM', sequelize.col('total_amount')), 'total_revenue'],
        [sequelize.fn('AVG', sequelize.col('total_amount')), 'average_order_value']
      ],
      where: {
        order_date: {
          [Op.gte]: sixMonthsAgo
        },
        total_amount: { [Op.ne]: null },
        status: { [Op.ne]: 'CANCELLED' }
      },
      group: [sequelize.fn('DATE_TRUNC', 'month', sequelize.col('order_date'))],
      order: [[sequelize.fn('DATE_TRUNC', 'month', sequelize.col('order_date')), 'ASC']],
      raw: true
    });
    
    // Format the data for charts
    const formattedTrend = salesTrend.map(item => ({
      month: new Date(item.month).toLocaleDateString('ar-SA', { 
        year: 'numeric', 
        month: 'long' 
      }),
      orders_count: parseInt(item.orders_count),
      total_revenue: parseFloat(item.total_revenue || 0),
      average_order_value: parseFloat(item.average_order_value || 0)
    }));
    
    sendSuccessResponse(res, 200, 'تم جلب اتجاه المبيعات بنجاح', { 
      trend: formattedTrend,
      period: 'آخر 6 أشهر'
    });
  } catch (error) {
    console.error('Error in getSalesTrend:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب اتجاه المبيعات', error);
  }
};

// Get top selling products
exports.getTopProducts = async (req, res) => {
  try {
    const { limit = 5 } = req.query;

    const topProducts = await SalesOrderItem.findAll({
      attributes: [
        'product_id',
        [sequelize.fn('SUM', sequelize.col('quantity_ordered')), 'total_ordered'],
        [sequelize.fn('SUM', sequelize.col('quantity_delivered')), 'total_delivered'],
        [sequelize.fn('COUNT', sequelize.col('SalesOrderItem.id')), 'orders_count'],
        [sequelize.fn('SUM',
          sequelize.literal('quantity_delivered * COALESCE(unit_price, 0)')
        ), 'total_revenue']
      ],
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['name', 'code', 'unit']
        }
      ],
      group: ['product_id', 'product.id'],
      order: [[sequelize.fn('SUM', sequelize.col('quantity_delivered')), 'DESC']],
      limit: parseInt(limit),
      raw: false
    });

    const formattedProducts = topProducts.map(item => ({
      product_id: item.product_id,
      product_name: item.product.name,
      product_code: item.product.code,
      unit: item.product.unit,
      total_ordered: parseInt(item.dataValues.total_ordered),
      total_delivered: parseInt(item.dataValues.total_delivered),
      orders_count: parseInt(item.dataValues.orders_count),
      total_revenue: parseFloat(item.dataValues.total_revenue || 0),
      delivery_rate: item.dataValues.total_ordered > 0
        ? Math.round((item.dataValues.total_delivered / item.dataValues.total_ordered) * 100)
        : 0
    }));

    sendSuccessResponse(res, 200, 'تم جلب المنتجات الأكثر مبيعاً بنجاح', {
      products: formattedProducts,
      limit: parseInt(limit)
    });
  } catch (error) {
    console.error('Error in getTopProducts:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب المنتجات الأكثر مبيعاً', error);
  }
};

// Get top suppliers
exports.getTopSuppliers = async (req, res) => {
  try {
    const { limit = 5 } = req.query;

    const topSuppliers = await PurchaseOrder.findAll({
      attributes: [
        'supplier_id',
        [sequelize.fn('COUNT', sequelize.col('PurchaseOrder.id')), 'orders_count'],
        [sequelize.fn('SUM', sequelize.col('total_amount')), 'total_value'],
        [sequelize.fn('AVG', sequelize.col('total_amount')), 'average_order_value']
      ],
      include: [
        {
          model: Supplier,
          as: 'supplier',
          attributes: ['name', 'contact_name', 'phone', 'email']
        }
      ],
      where: {
        total_amount: { [Op.ne]: null }
      },
      group: ['supplier_id', 'supplier.id'],
      order: [[sequelize.fn('SUM', sequelize.col('total_amount')), 'DESC']],
      limit: parseInt(limit),
      raw: false
    });

    const formattedSuppliers = topSuppliers.map(item => ({
      supplier_id: item.supplier_id,
      supplier_name: item.supplier.name,
      contact_name: item.supplier.contact_name,
      phone: item.supplier.phone,
      email: item.supplier.email,
      orders_count: parseInt(item.dataValues.orders_count),
      total_value: parseFloat(item.dataValues.total_value || 0),
      average_order_value: parseFloat(item.dataValues.average_order_value || 0)
    }));

    sendSuccessResponse(res, 200, 'تم جلب الموردين الأعلى توريداً بنجاح', {
      suppliers: formattedSuppliers,
      limit: parseInt(limit)
    });
  } catch (error) {
    console.error('Error in getTopSuppliers:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب الموردين الأعلى توريداً', error);
  }
};

// Get inventory alerts (low stock and out of stock)
exports.getInventoryAlerts = async (req, res) => {
  try {
    // Low stock products
    const lowStockProducts = await Inventory.findAll({
      where: {
        [Op.and]: [
          { minimum_quantity: { [Op.ne]: null } },
          { quantity: { [Op.lte]: { [Op.col]: 'minimum_quantity' } } },
          { quantity: { [Op.gt]: 0 } }
        ]
      },
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['name', 'code', 'unit']
        },
        {
          model: Warehouse,
          as: 'warehouse',
          attributes: ['name', 'code', 'location']
        }
      ],
      order: [
        [sequelize.literal('quantity::float / NULLIF(minimum_quantity, 0)'), 'ASC']
      ]
    });

    // Out of stock products
    const outOfStockProducts = await Inventory.findAll({
      where: { quantity: 0 },
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['name', 'code', 'unit']
        },
        {
          model: Warehouse,
          as: 'warehouse',
          attributes: ['name', 'code', 'location']
        }
      ]
    });

    // Format low stock data
    const formattedLowStock = lowStockProducts.map(item => ({
      inventory_id: item.id,
      product: {
        id: item.product_id,
        name: item.product.name,
        code: item.product.code,
        unit: item.product.unit
      },
      warehouse: {
        id: item.warehouse_id,
        name: item.warehouse.name,
        code: item.warehouse.code,
        location: item.warehouse.location
      },
      current_quantity: item.quantity,
      minimum_quantity: item.minimum_quantity,
      shortage: item.minimum_quantity - item.quantity,
      stock_level_percentage: item.minimum_quantity > 0
        ? Math.round((item.quantity / item.minimum_quantity) * 100)
        : 0,
      alert_level: item.quantity === 0 ? 'critical' :
                   item.quantity <= (item.minimum_quantity * 0.5) ? 'high' : 'medium'
    }));

    // Format out of stock data
    const formattedOutOfStock = outOfStockProducts.map(item => ({
      inventory_id: item.id,
      product: {
        id: item.product_id,
        name: item.product.name,
        code: item.product.code,
        unit: item.product.unit
      },
      warehouse: {
        id: item.warehouse_id,
        name: item.warehouse.name,
        code: item.warehouse.code,
        location: item.warehouse.location
      },
      minimum_quantity: item.minimum_quantity,
      last_checked_at: item.last_checked_at,
      alert_level: 'critical'
    }));

    const alerts = {
      low_stock: {
        count: formattedLowStock.length,
        items: formattedLowStock
      },
      out_of_stock: {
        count: formattedOutOfStock.length,
        items: formattedOutOfStock
      },
      total_alerts: formattedLowStock.length + formattedOutOfStock.length
    };

    sendSuccessResponse(res, 200, 'تم جلب تنبيهات المخزون بنجاح', { alerts });
  } catch (error) {
    console.error('Error in getInventoryAlerts:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب تنبيهات المخزون', error);
  }
};

// Get orders status distribution
exports.getOrdersStatus = async (req, res) => {
  try {
    // Purchase orders status
    const purchaseOrdersStatus = await PurchaseOrder.findAll({
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('SUM', sequelize.col('total_amount')), 'total_value']
      ],
      where: {
        total_amount: { [Op.ne]: null }
      },
      group: ['status'],
      raw: true
    });

    // Sales orders status
    const salesOrdersStatus = await SalesOrder.findAll({
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('SUM', sequelize.col('total_amount')), 'total_value']
      ],
      where: {
        total_amount: { [Op.ne]: null }
      },
      group: ['status'],
      raw: true
    });

    // Format purchase orders data
    const formattedPurchaseOrders = purchaseOrdersStatus.map(item => ({
      status: item.status,
      status_arabic: getStatusInArabic(item.status),
      count: parseInt(item.count),
      total_value: parseFloat(item.total_value || 0),
      percentage: 0 // Will be calculated below
    }));

    // Format sales orders data
    const formattedSalesOrders = salesOrdersStatus.map(item => ({
      status: item.status,
      status_arabic: getStatusInArabic(item.status),
      count: parseInt(item.count),
      total_value: parseFloat(item.total_value || 0),
      percentage: 0 // Will be calculated below
    }));

    // Calculate percentages
    const totalPurchaseOrders = formattedPurchaseOrders.reduce((sum, item) => sum + item.count, 0);
    const totalSalesOrders = formattedSalesOrders.reduce((sum, item) => sum + item.count, 0);

    formattedPurchaseOrders.forEach(item => {
      item.percentage = totalPurchaseOrders > 0
        ? Math.round((item.count / totalPurchaseOrders) * 100)
        : 0;
    });

    formattedSalesOrders.forEach(item => {
      item.percentage = totalSalesOrders > 0
        ? Math.round((item.count / totalSalesOrders) * 100)
        : 0;
    });

    // Get pending items summary
    const pendingPurchaseItems = await PurchaseOrderItem.count({
      where: {
        quantity_received: {
          [Op.lt]: sequelize.col('quantity_ordered')
        }
      }
    });

    const pendingSalesItems = await SalesOrderItem.count({
      where: {
        quantity_delivered: {
          [Op.lt]: sequelize.col('quantity_ordered')
        }
      }
    });

    const ordersStatus = {
      purchase_orders: {
        total: totalPurchaseOrders,
        status_distribution: formattedPurchaseOrders,
        pending_items: pendingPurchaseItems
      },
      sales_orders: {
        total: totalSalesOrders,
        status_distribution: formattedSalesOrders,
        pending_items: pendingSalesItems
      },
      summary: {
        total_orders: totalPurchaseOrders + totalSalesOrders,
        total_pending_items: pendingPurchaseItems + pendingSalesItems
      }
    };

    sendSuccessResponse(res, 200, 'تم جلب حالة الطلبات بنجاح', { orders_status: ordersStatus });
  } catch (error) {
    console.error('Error in getOrdersStatus:', error);
    sendErrorResponse(res, 500, 'خطأ في جلب حالة الطلبات', error);
  }
};

// Helper function to translate status to Arabic
function getStatusInArabic(status) {
  const statusMap = {
    'NEW': 'جديد',
    'PARTIALLY_DELIVERED': 'مسلم جزئياً',
    'COMPLETED': 'مكتمل',
    'CANCELLED': 'ملغي'
  };
  return statusMap[status] || status;
}
