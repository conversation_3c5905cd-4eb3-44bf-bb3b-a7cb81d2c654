'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('purchase_orders', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      supplier_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'suppliers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      order_number: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      status: {
        type: Sequelize.ENUM('NEW', 'PARTIALLY_DELIVERED', 'COMPLETED', 'CANCELLED'),
        allowNull: false,
        defaultValue: 'NEW'
      },
      total_amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      order_date: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      delivery_date: {
        type: Sequelize.DATE,
        allowNull: true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('purchase_orders', ['supplier_id'], {
      name: 'purchase_orders_supplier_id_index'
    });

    await queryInterface.addIndex('purchase_orders', ['order_number'], {
      name: 'purchase_orders_order_number_index',
      unique: true
    });

    await queryInterface.addIndex('purchase_orders', ['status'], {
      name: 'purchase_orders_status_index'
    });

    await queryInterface.addIndex('purchase_orders', ['order_date'], {
      name: 'purchase_orders_order_date_index'
    });

    await queryInterface.addIndex('purchase_orders', ['delivery_date'], {
      name: 'purchase_orders_delivery_date_index'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('purchase_orders');
  }
};
