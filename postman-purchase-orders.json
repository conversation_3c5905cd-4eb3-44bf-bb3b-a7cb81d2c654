{"info": {"name": "Purchase Orders Management API", "description": "مجموعة أمثلة لاختبار API إدارة أوامر الشراء", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Purchase Orders", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/purchase-orders", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders"]}}}, {"name": "Get Purchase Orders with Pagination", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/purchase-orders?page=1&limit=5", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "5"}]}}}, {"name": "Get Purchase Orders by Supplier", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/purchase-orders?supplier_id=1", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders"], "query": [{"key": "supplier_id", "value": "1"}]}}}, {"name": "Get Purchase Orders by Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/purchase-orders?status=NEW", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders"], "query": [{"key": "status", "value": "NEW"}]}}}, {"name": "Get Purchase Orders by Date Range", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/purchase-orders?start_date=2024-12-01&end_date=2024-12-31", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders"], "query": [{"key": "start_date", "value": "2024-12-01"}, {"key": "end_date", "value": "2024-12-31"}]}}}, {"name": "Get Purchase Order by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/purchase-orders/1", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders", "1"]}}}, {"name": "Create Purchase Order - Basic", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"supplier_id\": 1,\n  \"delivery_date\": \"2025-01-15T10:00:00.000Z\",\n  \"notes\": \"طلب شراء للمنتجات التقنية الجديدة\",\n  \"items\": [\n    {\n      \"product_id\": 1,\n      \"quantity_ordered\": 5,\n      \"unit_price\": 2500.00\n    },\n    {\n      \"product_id\": 2,\n      \"quantity_ordered\": 10,\n      \"unit_price\": 150.00\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/purchase-orders", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders"]}}}, {"name": "Create Purchase Order - Without Prices", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"supplier_id\": 2,\n  \"delivery_date\": \"2025-01-20T14:00:00.000Z\",\n  \"notes\": \"طلب شراء عاجل للملحقات\",\n  \"items\": [\n    {\n      \"product_id\": 3,\n      \"quantity_ordered\": 8\n    },\n    {\n      \"product_id\": 4,\n      \"quantity_ordered\": 12\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/purchase-orders", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders"]}}}, {"name": "Update Purchase Order Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"PARTIALLY_DELIVERED\",\n  \"notes\": \"تم استلام جزء من الطلب\"\n}"}, "url": {"raw": "{{baseUrl}}/api/purchase-orders/1", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders", "1"]}}}, {"name": "Update Purchase Order Delivery Date", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"delivery_date\": \"2025-02-01T10:00:00.000Z\",\n  \"notes\": \"تم تأجيل موعد التسليم\"\n}"}, "url": {"raw": "{{baseUrl}}/api/purchase-orders/1", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders", "1"]}}}, {"name": "Receive Products - Partial", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"warehouse_id\": 1,\n  \"items\": [\n    {\n      \"product_id\": 1,\n      \"quantity_received\": 3\n    },\n    {\n      \"product_id\": 2,\n      \"quantity_received\": 5\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/purchase-orders/1/receive", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders", "1", "receive"]}}}, {"name": "Receive Products - Complete", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"warehouse_id\": 1,\n  \"items\": [\n    {\n      \"product_id\": 1,\n      \"quantity_received\": 2\n    },\n    {\n      \"product_id\": 2,\n      \"quantity_received\": 5\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/purchase-orders/1/receive", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders", "1", "receive"]}}}, {"name": "Delete Purchase Order", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/purchase-orders/1", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders", "1"]}}}, {"name": "Get Purchase Orders Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/purchase-orders/stats", "host": ["{{baseUrl}}"], "path": ["api", "purchase-orders", "stats"]}}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}