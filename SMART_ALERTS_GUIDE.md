# دليل التنبيهات الذكية للمخزون
## Smart Inventory Alerts Guide

هذا الدليل يوضح كيفية استخدام وحدة التنبيهات الذكية في نظام إدارة المخزون.

---

## 🧠 المنطق الذكي للتنبيهات

### آلية العمل التلقائية:
1. **عند كل حركة مخزون** (IN/OUT) يتم فحص المستوى الحالي
2. **مقارنة مع الحد الأدنى** المحدد لكل منتج
3. **إنشاء تنبيه ذكي** إذا كان المستوى منخفض
4. **تجنب التكرار** - لا ينشئ تنبيه مكرر لنفس المنتج/المستودع
5. **حل تلقائي** عندما يعود المستوى فوق الحد الأدنى

### مستويات الخطورة:
- **CRITICAL** 🔴: `quantity = 0` (نفاد المخزون)
- **HIGH** 🟠: `quantity < minimum_quantity / 2` (أقل من نصف الحد الأدنى)
- **MEDIUM** 🟡: `quantity < minimum_quantity` (تحت الحد الأدنى)

---

## 📊 هيكل بيانات التنبيه

```json
{
  "id": 1,
  "product_id": 5,
  "warehouse_id": 2,
  "type": "LOW_STOCK",
  "level": "HIGH",
  "quantity": 2,
  "minimum_quantity": 10,
  "alert_message": "مخزون منخفض جداً - الكمية الحالية: 2، الحد الأدنى: 10",
  "status": "NEW",
  "created_at": "2024-12-27T10:30:00.000Z",
  "updated_at": "2024-12-27T10:30:00.000Z",
  "product": {
    "id": 5,
    "name": "كيبورد ميكانيكي للألعاب",
    "code": "KB-GAMING-01",
    "unit": "قطعة"
  },
  "warehouse": {
    "id": 2,
    "name": "مستودع جدة التجاري",
    "code": "WH-JED-01",
    "location": "جدة"
  },
  "age_hours": 2,
  "priority_score": 75,
  "is_critical": false,
  "shortage": 8
}
```

---

## 🌐 API التنبيهات

### 1. جلب جميع التنبيهات
```http
GET /api/alerts?page=1&limit=10&level=CRITICAL&status=NEW
```

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "alerts": [...],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalItems": 25,
      "itemsPerPage": 10
    }
  }
}
```

### 2. التنبيهات النشطة (مجمعة)
```http
GET /api/alerts/active
```

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "alerts": [...],
    "grouped_alerts": {
      "critical": [...],
      "high": [...],
      "medium": [...]
    },
    "total_count": 15,
    "critical_count": 3,
    "high_count": 7,
    "medium_count": 5
  }
}
```

### 3. إحصائيات التنبيهات
```http
GET /api/alerts/stats
```

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "stats": {
      "by_level": {
        "critical": 3,
        "high": 7,
        "medium": 5
      },
      "by_status": {
        "new": 10,
        "seen": 5,
        "resolved": 20
      },
      "total_active": 15,
      "total_alerts": 35,
      "alerts_today": 8,
      "alerts_this_week": 22,
      "most_problematic_products": [
        {
          "product_id": 5,
          "product_name": "كيبورد ميكانيكي",
          "product_code": "KB-GAMING-01",
          "alert_count": 5,
          "latest_alert": "2024-12-27T10:30:00.000Z"
        }
      ]
    }
  }
}
```

### 4. حل التنبيه
```http
PUT /api/alerts/1/resolve
Content-Type: application/json

{
  "resolution_note": "تم تجديد المخزون من المورد الرئيسي"
}
```

### 5. حل عدة تنبيهات
```http
POST /api/alerts/bulk-resolve
Content-Type: application/json

{
  "alert_ids": [1, 2, 3, 4],
  "resolution_note": "تم حل جميع التنبيهات بعد تجديد المخزون"
}
```

### 6. فحص جميع المخزون
```http
POST /api/alerts/check-all
```

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "total_items_checked": 150,
    "alerts_created": 8,
    "alerts_resolved": 3,
    "message": "تم فحص 150 عنصر، إنشاء 8 تنبيه جديد، وحل 3 تنبيه"
  }
}
```

---

## 🔄 التكامل التلقائي

### مع حركة المخزون:
```javascript
// عند إضافة حركة مخزون جديدة
const movement = await StockMovement.create({
  product_id: 5,
  warehouse_id: 2,
  type: 'OUT',
  quantity: 8
});

// يتم تلقائياً:
// 1. تحديث الجرد
// 2. فحص المستوى الجديد
// 3. إنشاء تنبيه إذا لزم الأمر
```

### مع أوامر البيع:
```javascript
// عند تسليم منتجات من أمر بيع
await salesOrder.deliverProducts({
  items: [{ product_id: 5, quantity_delivered: 3 }]
});

// يتم تلقائياً إنشاء تنبيه إذا انخفض المخزون
```

---

## 🎨 استخدام في الواجهة الأمامية

### 1. لوحة التنبيهات
```javascript
const fetchActiveAlerts = async () => {
  const response = await fetch('/api/alerts/active');
  const data = await response.json();
  
  if (data.success) {
    updateAlertBadge(data.data.total_count);
    displayAlertsByLevel(data.data.grouped_alerts);
  }
};

// تحديث كل 30 ثانية
setInterval(fetchActiveAlerts, 30000);
```

### 2. مؤشر التنبيهات
```javascript
const updateAlertBadge = (count) => {
  const badge = document.getElementById('alert-badge');
  badge.textContent = count;
  badge.className = count > 0 ? 'badge badge-danger' : 'badge badge-secondary';
};
```

### 3. عرض التنبيهات حسب المستوى
```javascript
const displayAlertsByLevel = (groupedAlerts) => {
  const criticalContainer = document.getElementById('critical-alerts');
  const highContainer = document.getElementById('high-alerts');
  const mediumContainer = document.getElementById('medium-alerts');
  
  criticalContainer.innerHTML = renderAlerts(groupedAlerts.critical, 'danger');
  highContainer.innerHTML = renderAlerts(groupedAlerts.high, 'warning');
  mediumContainer.innerHTML = renderAlerts(groupedAlerts.medium, 'info');
};

const renderAlerts = (alerts, alertClass) => {
  return alerts.map(alert => `
    <div class="alert alert-${alertClass} alert-dismissible">
      <strong>${alert.product.name}</strong>
      <p>${alert.alert_message}</p>
      <small>المستودع: ${alert.warehouse.name} | منذ ${alert.age_hours} ساعة</small>
      <button onclick="resolveAlert(${alert.id})" class="btn btn-sm btn-outline-secondary">
        حل التنبيه
      </button>
    </div>
  `).join('');
};
```

### 4. حل التنبيهات
```javascript
const resolveAlert = async (alertId, note = '') => {
  try {
    const response = await fetch(`/api/alerts/${alertId}/resolve`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ resolution_note: note })
    });
    
    if (response.ok) {
      // إعادة تحميل التنبيهات
      fetchActiveAlerts();
      showNotification('تم حل التنبيه بنجاح', 'success');
    }
  } catch (error) {
    showNotification('خطأ في حل التنبيه', 'error');
  }
};
```

---

## 📱 أمثلة عملية

### 1. مراقبة المخزون الحرج
```bash
# جلب التنبيهات الحرجة فقط
curl "http://localhost:3000/api/alerts?level=CRITICAL&status=NEW"
```

### 2. تقرير يومي للتنبيهات
```bash
# إحصائيات التنبيهات
curl http://localhost:3000/api/alerts/stats
```

### 3. فحص دوري للمخزون
```bash
# فحص جميع عناصر المخزون (يمكن جدولته)
curl -X POST http://localhost:3000/api/alerts/check-all
```

### 4. حل التنبيهات بعد التجديد
```bash
# حل عدة تنبيهات بعد استلام شحنة
curl -X POST http://localhost:3000/api/alerts/bulk-resolve \
  -H "Content-Type: application/json" \
  -d '{
    "alert_ids": [1, 2, 3],
    "resolution_note": "تم استلام شحنة جديدة من المورد"
  }'
```

---

## ⚙️ إعدادات متقدمة

### 1. تخصيص الحد الأدنى لكل منتج
```sql
-- تحديث الحد الأدنى لمنتج معين
UPDATE products SET minimum_quantity = 15 WHERE id = 5;
```

### 2. جدولة فحص دوري
```javascript
// فحص كل ساعة
const cron = require('node-cron');

cron.schedule('0 * * * *', async () => {
  try {
    const response = await fetch('http://localhost:3000/api/alerts/check-all', {
      method: 'POST'
    });
    console.log('تم فحص المخزون تلقائياً');
  } catch (error) {
    console.error('خطأ في الفحص التلقائي:', error);
  }
});
```

### 3. إشعارات البريد الإلكتروني
```javascript
const sendEmailAlert = async (alert) => {
  const emailContent = `
    تنبيه مخزون ${alert.level === 'CRITICAL' ? 'حرج' : 'منخفض'}
    
    المنتج: ${alert.product.name}
    المستودع: ${alert.warehouse.name}
    الكمية الحالية: ${alert.quantity}
    الحد الأدنى: ${alert.minimum_quantity}
    
    يرجى اتخاذ الإجراء المناسب.
  `;
  
  // إرسال البريد الإلكتروني
  await sendEmail({
    to: '<EMAIL>',
    subject: `تنبيه مخزون: ${alert.product.name}`,
    text: emailContent
  });
};
```

---

## 🚀 أفضل الممارسات

1. **مراقبة دورية**: فحص التنبيهات كل 30 ثانية في الواجهة
2. **تصنيف الأولويات**: التركيز على التنبيهات الحرجة أولاً
3. **حل سريع**: حل التنبيهات فور معالجة المشكلة
4. **تحليل الاتجاهات**: مراجعة المنتجات الأكثر إشكالية
5. **أتمتة الإجراءات**: ربط التنبيهات بأوامر شراء تلقائية

هذه الوحدة توفر نظام تنبيهات ذكي ومتقدم لضمان عدم نفاد المخزون! 🚨
