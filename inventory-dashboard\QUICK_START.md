# 🚀 دليل البدء السريع - لوحة تحكم إدارة المخزون

## خطوات التشغيل السريع

### 1. تأكد من تشغيل Backend
```bash
# في مجلد backend
cd ../backend
npm run dev
# يجب أن يعمل على http://localhost:3000
```

### 2. تثبيت تبعيات Frontend
```bash
# في مجلد inventory-dashboard
npm install
```

### 3. تشغيل التطبيق
```bash
npm run dev
```

### 4. فتح المتصفح
```
http://localhost:5173
```

## 🎯 الصفحات المتاحة حالياً

### ✅ مكتملة ومتصلة بـ API:
- **لوحة التحكم** (`/dashboard`) - KPIs والإحصائيات
- **إدارة المنتجات** (`/products`) - عرض وإدارة المنتجات

### 🚧 قيد التطوير (واجهات أساسية):
- الموردين (`/suppliers`)
- المستودعات (`/warehouses`) 
- الجرد (`/inventory`)
- حركة المخزون (`/movements`)
- أوامر الشراء (`/purchase-orders`)
- أوامر البيع (`/sales-orders`)
- التقارير (`/reports`)
- التنبيهات (`/alerts`)

## 🔧 الميزات المتاحة

### لوحة التحكم:
- عرض KPIs من Backend
- رسم بياني للمبيعات
- أفضل المنتجات مبيعاً
- إجراءات سريعة

### إدارة المنتجات:
- عرض قائمة المنتجات
- البحث والتصفية
- حالة المخزون (متوفر/منخفض/نافد)
- حذف المنتجات
- Pagination

### التنبيهات:
- تنبيهات في الوقت الفعلي في Topbar
- عداد التنبيهات غير المقروءة
- تصنيف حسب المستوى (حرج/عالي/متوسط)

## 🎨 التصميم

- **RTL Support** - دعم اللغة العربية
- **Responsive Design** - متجاوب مع جميع الأجهزة
- **Modern UI** - واجهة حديثة وأنيقة
- **Dark/Light Theme** - ألوان متوازنة

## 🔗 API Integration

جميع الخدمات متصلة بـ Backend:
- `dashboardService` - بيانات لوحة التحكم
- `productService` - إدارة المنتجات
- `supplierService` - إدارة الموردين
- `warehouseService` - إدارة المستودعات
- `inventoryService` - إدارة الجرد
- `alertService` - إدارة التنبيهات

## 🚨 ملاحظات مهمة

1. **Backend مطلوب**: تأكد من تشغيل Backend على المنفذ 3000
2. **البيانات التجريبية**: استخدم البيانات الموجودة في Backend
3. **التطوير المستمر**: باقي الصفحات قيد التطوير

## 🎯 الخطوات التالية

1. إكمال باقي صفحات النظام
2. إضافة نماذج إضافة/تعديل البيانات
3. تحسين التقارير والرسوم البيانية
4. إضافة المزيد من الميزات التفاعلية

---

**🎉 مبروك! لوحة التحكم جاهزة للاستخدام**
