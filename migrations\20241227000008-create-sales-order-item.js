'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('sales_order_items', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      sales_order_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'sales_orders',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      product_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'products',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      quantity_ordered: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      quantity_delivered: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      unit_price: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('sales_order_items', ['sales_order_id'], {
      name: 'sales_order_items_sales_order_id_index'
    });

    await queryInterface.addIndex('sales_order_items', ['product_id'], {
      name: 'sales_order_items_product_id_index'
    });

    await queryInterface.addIndex('sales_order_items', ['quantity_ordered'], {
      name: 'sales_order_items_quantity_ordered_index'
    });

    await queryInterface.addIndex('sales_order_items', ['quantity_delivered'], {
      name: 'sales_order_items_quantity_delivered_index'
    });

    await queryInterface.addIndex('sales_order_items', ['sales_order_id', 'product_id'], {
      name: 'sales_order_items_order_product_index',
      unique: true
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('sales_order_items');
  }
};
