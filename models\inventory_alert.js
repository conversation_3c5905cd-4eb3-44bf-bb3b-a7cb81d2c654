'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class InventoryAlert extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Define associations here
      InventoryAlert.belongsTo(models.Product, {
        foreignKey: 'product_id',
        as: 'product'
      });
      
      InventoryAlert.belongsTo(models.Warehouse, {
        foreignKey: 'warehouse_id',
        as: 'warehouse'
      });
    }

    // Instance method to check if alert is new
    isNew() {
      return this.status === 'NEW';
    }

    // Instance method to check if alert is seen
    isSeen() {
      return this.status === 'SEEN';
    }

    // Instance method to check if alert is resolved
    isResolved() {
      return this.status === 'RESOLVED';
    }

    // Instance method to check if alert is critical
    isCritical() {
      return this.level === 'CRITICAL';
    }

    // Instance method to get alert priority score
    getPriorityScore() {
      const levelScores = {
        'CRITICAL': 100,
        'HIGH': 75,
        'MEDIUM': 50
      };
      return levelScores[this.level] || 0;
    }

    // Instance method to get alert age in hours
    getAgeInHours() {
      const now = new Date();
      const created = new Date(this.created_at);
      return Math.floor((now - created) / (1000 * 60 * 60));
    }

    // Static method to create alert with smart logic
    static async createSmartAlert(productId, warehouseId, currentQuantity, minimumQuantity, transaction = null) {
      try {
        // Check if there's already an active alert for this product-warehouse combination
        const existingAlert = await this.findOne({
          where: {
            product_id: productId,
            warehouse_id: warehouseId,
            status: { [sequelize.Sequelize.Op.in]: ['NEW', 'SEEN'] }
          },
          transaction
        });

        // If alert exists and quantity hasn't changed significantly, don't create new one
        if (existingAlert && Math.abs(existingAlert.quantity - currentQuantity) <= 1) {
          return existingAlert;
        }

        // Determine alert type and level
        let alertType, alertLevel, alertMessage;

        if (currentQuantity === 0) {
          alertType = 'OUT_OF_STOCK';
          alertLevel = 'CRITICAL';
          alertMessage = `نفاد المخزون - المنتج غير متوفر`;
        } else if (currentQuantity < minimumQuantity) {
          alertType = 'LOW_STOCK';
          
          if (currentQuantity < minimumQuantity / 2) {
            alertLevel = 'HIGH';
            alertMessage = `مخزون منخفض جداً - الكمية الحالية: ${currentQuantity}، الحد الأدنى: ${minimumQuantity}`;
          } else {
            alertLevel = 'MEDIUM';
            alertMessage = `مخزون منخفض - الكمية الحالية: ${currentQuantity}، الحد الأدنى: ${minimumQuantity}`;
          }
        } else {
          // Quantity is above minimum, resolve existing alerts if any
          if (existingAlert) {
            await existingAlert.update({
              status: 'RESOLVED',
              alert_message: `تم حل التنبيه - الكمية الحالية: ${currentQuantity}`
            }, { transaction });
          }
          return null;
        }

        // Resolve existing alert if creating a new one
        if (existingAlert) {
          await existingAlert.update({ status: 'RESOLVED' }, { transaction });
        }

        // Create new alert
        const newAlert = await this.create({
          product_id: productId,
          warehouse_id: warehouseId,
          type: alertType,
          level: alertLevel,
          quantity: currentQuantity,
          minimum_quantity: minimumQuantity,
          alert_message: alertMessage,
          status: 'NEW'
        }, { transaction });

        return newAlert;
      } catch (error) {
        console.error('Error creating smart alert:', error);
        throw error;
      }
    }

    // Static method to get active alerts
    static async getActiveAlerts() {
      return await this.findAll({
        where: {
          status: { [sequelize.Sequelize.Op.in]: ['NEW', 'SEEN'] }
        },
        include: [
          {
            model: sequelize.models.Product,
            as: 'product',
            attributes: ['name', 'code', 'unit']
          },
          {
            model: sequelize.models.Warehouse,
            as: 'warehouse',
            attributes: ['name', 'code', 'location']
          }
        ],
        order: [
          ['level', 'DESC'], // CRITICAL first
          ['created_at', 'ASC'] // Oldest first
        ]
      });
    }

    // Static method to get alerts by level
    static async getAlertsByLevel(level) {
      return await this.findAll({
        where: {
          level: level,
          status: { [sequelize.Sequelize.Op.in]: ['NEW', 'SEEN'] }
        },
        include: [
          {
            model: sequelize.models.Product,
            as: 'product'
          },
          {
            model: sequelize.models.Warehouse,
            as: 'warehouse'
          }
        ],
        order: [['created_at', 'ASC']]
      });
    }

    // Static method to get alert statistics
    static async getAlertStats() {
      const stats = await this.findAll({
        attributes: [
          'level',
          'status',
          [sequelize.Sequelize.fn('COUNT', sequelize.Sequelize.col('id')), 'count']
        ],
        group: ['level', 'status'],
        raw: true
      });

      const formattedStats = {
        by_level: {
          critical: 0,
          high: 0,
          medium: 0
        },
        by_status: {
          new: 0,
          seen: 0,
          resolved: 0
        },
        total_active: 0
      };

      stats.forEach(stat => {
        const count = parseInt(stat.count);
        
        // Count by level
        if (stat.level === 'CRITICAL') formattedStats.by_level.critical += count;
        else if (stat.level === 'HIGH') formattedStats.by_level.high += count;
        else if (stat.level === 'MEDIUM') formattedStats.by_level.medium += count;
        
        // Count by status
        if (stat.status === 'NEW') formattedStats.by_status.new += count;
        else if (stat.status === 'SEEN') formattedStats.by_status.seen += count;
        else if (stat.status === 'RESOLVED') formattedStats.by_status.resolved += count;
        
        // Count active alerts
        if (stat.status === 'NEW' || stat.status === 'SEEN') {
          formattedStats.total_active += count;
        }
      });

      return formattedStats;
    }
  }

  InventoryAlert.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'products',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'معرف المنتج مطلوب'
        },
        isInt: {
          msg: 'معرف المنتج يجب أن يكون رقم صحيح'
        }
      }
    },
    warehouse_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'warehouses',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'معرف المستودع مطلوب'
        },
        isInt: {
          msg: 'معرف المستودع يجب أن يكون رقم صحيح'
        }
      }
    },
    type: {
      type: DataTypes.ENUM('LOW_STOCK', 'OUT_OF_STOCK'),
      allowNull: false,
      validate: {
        isIn: {
          args: [['LOW_STOCK', 'OUT_OF_STOCK']],
          msg: 'نوع التنبيه يجب أن يكون LOW_STOCK أو OUT_OF_STOCK'
        }
      }
    },
    level: {
      type: DataTypes.ENUM('CRITICAL', 'HIGH', 'MEDIUM'),
      allowNull: false,
      validate: {
        isIn: {
          args: [['CRITICAL', 'HIGH', 'MEDIUM']],
          msg: 'مستوى التنبيه يجب أن يكون CRITICAL أو HIGH أو MEDIUM'
        }
      }
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: {
          args: [0],
          msg: 'الكمية يجب أن تكون أكبر من أو تساوي 0'
        }
      }
    },
    minimum_quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: {
          args: [0],
          msg: 'الحد الأدنى يجب أن يكون أكبر من أو يساوي 0'
        }
      }
    },
    alert_message: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'رسالة التنبيه مطلوبة'
        }
      }
    },
    status: {
      type: DataTypes.ENUM('NEW', 'SEEN', 'RESOLVED'),
      allowNull: false,
      defaultValue: 'NEW',
      validate: {
        isIn: {
          args: [['NEW', 'SEEN', 'RESOLVED']],
          msg: 'حالة التنبيه يجب أن تكون NEW أو SEEN أو RESOLVED'
        }
      }
    }
  }, {
    sequelize,
    modelName: 'InventoryAlert',
    tableName: 'inventory_alerts',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return InventoryAlert;
};
