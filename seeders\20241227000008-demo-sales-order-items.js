'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Get actual sales order and product IDs from database
    const salesOrders = await queryInterface.sequelize.query(
      'SELECT id FROM sales_orders ORDER BY id LIMIT 4',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );
    
    const products = await queryInterface.sequelize.query(
      'SELECT id FROM products ORDER BY id LIMIT 6',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );
    
    if (salesOrders.length > 0 && products.length > 0) {
      await queryInterface.bulkInsert('sales_order_items', [
        // Items for first sales order (NEW)
        {
          sales_order_id: salesOrders[0].id,
          product_id: products[0].id,
          quantity_ordered: 2,
          quantity_delivered: 0,
          unit_price: 2500.00,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          sales_order_id: salesOrders[0].id,
          product_id: products[1].id,
          quantity_ordered: 5,
          quantity_delivered: 0,
          unit_price: 150.00,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          sales_order_id: salesOrders[0].id,
          product_id: products[2].id,
          quantity_ordered: 4,
          quantity_delivered: 0,
          unit_price: 800.00,
          created_at: new Date(),
          updated_at: new Date()
        },
        
        // Items for second sales order (PARTIALLY_DELIVERED)
        {
          sales_order_id: salesOrders[1].id,
          product_id: products[1].id,
          quantity_ordered: 8,
          quantity_delivered: 3,
          unit_price: 150.00,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          sales_order_id: salesOrders[1].id,
          product_id: products[3].id,
          quantity_ordered: 6,
          quantity_delivered: 2,
          unit_price: 600.00,
          created_at: new Date(),
          updated_at: new Date()
        },
        
        // Items for third sales order (COMPLETED)
        {
          sales_order_id: salesOrders[2].id,
          product_id: products[0].id,
          quantity_ordered: 1,
          quantity_delivered: 1,
          unit_price: 2500.00,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          sales_order_id: salesOrders[2].id,
          product_id: products[4].id,
          quantity_ordered: 10,
          quantity_delivered: 10,
          unit_price: 425.00,
          created_at: new Date(),
          updated_at: new Date()
        },
        
        // Items for fourth sales order (NEW)
        {
          sales_order_id: salesOrders[3].id,
          product_id: products[2].id,
          quantity_ordered: 2,
          quantity_delivered: 0,
          unit_price: 800.00,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          sales_order_id: salesOrders[3].id,
          product_id: products[5].id,
          quantity_ordered: 4,
          quantity_delivered: 0,
          unit_price: 400.00,
          created_at: new Date(),
          updated_at: new Date()
        }
      ], {});
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('sales_order_items', null, {});
  }
};
