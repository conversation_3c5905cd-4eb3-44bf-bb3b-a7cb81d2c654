const express = require('express');
const router = express.Router();
const stockMovementController = require('../controllers/stockMovement.controller');

// Middleware for request logging
const requestLogger = (req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.originalUrl}`);
  next();
};

// Apply logging middleware to all routes
router.use(requestLogger);

// Validation middleware for stock movement creation
const validateMovementData = (req, res, next) => {
  const { product_id, warehouse_id, type, quantity } = req.body;
  
  // Check required fields for POST requests
  if (req.method === 'POST') {
    if (!product_id || !warehouse_id || !type || !quantity) {
      return res.status(400).json({
        success: false,
        message: 'معرف المنتج ومعرف المستودع ونوع الحركة والكمية مطلوبة',
        timestamp: new Date().toISOString()
      });
    }
  }
  
  // Validate product_id if provided
  if (product_id && (typeof product_id !== 'number' || product_id <= 0)) {
    return res.status(400).json({
      success: false,
      message: 'معرف المنتج يجب أن يكون رقم صحيح أكبر من 0',
      timestamp: new Date().toISOString()
    });
  }
  
  // Validate warehouse_id if provided
  if (warehouse_id && (typeof warehouse_id !== 'number' || warehouse_id <= 0)) {
    return res.status(400).json({
      success: false,
      message: 'معرف المستودع يجب أن يكون رقم صحيح أكبر من 0',
      timestamp: new Date().toISOString()
    });
  }
  
  // Validate type if provided
  if (type && !['IN', 'OUT'].includes(type)) {
    return res.status(400).json({
      success: false,
      message: 'نوع الحركة يجب أن يكون IN أو OUT',
      timestamp: new Date().toISOString()
    });
  }
  
  // Validate quantity if provided
  if (quantity !== undefined) {
    if (typeof quantity !== 'number' || quantity < 1) {
      return res.status(400).json({
        success: false,
        message: 'الكمية يجب أن تكون رقم صحيح أكبر من أو يساوي 1',
        timestamp: new Date().toISOString()
      });
    }
  }
  
  next();
};

// Validation middleware for ID parameter
const validateIdParam = (req, res, next) => {
  const { id } = req.params;
  
  if (!id || isNaN(parseInt(id))) {
    return res.status(400).json({
      success: false,
      message: 'معرف حركة المخزون يجب أن يكون رقم صحيح',
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

// Routes

/**
 * @route   GET /api/movements
 * @desc    Get all stock movements with optional filtering and pagination
 * @access  Public
 * @params  ?page=1&limit=10&product_id=1&warehouse_id=1&type=IN&start_date=2024-01-01&end_date=2024-12-31
 */
router.get('/', stockMovementController.getAllMovements);

/**
 * @route   GET /api/movements/stats
 * @desc    Get stock movements statistics
 * @access  Public
 */
router.get('/stats', stockMovementController.getMovementsStats);

/**
 * @route   GET /api/movements/:id
 * @desc    Get single stock movement by ID
 * @access  Public
 */
router.get('/:id', validateIdParam, stockMovementController.getMovementById);

/**
 * @route   POST /api/movements
 * @desc    Create new stock movement
 * @access  Public
 * @body    { product_id, warehouse_id, type, quantity, reference?, description?, moved_at? }
 */
router.post('/', validateMovementData, stockMovementController.createMovement);

/**
 * @route   DELETE /api/movements/:id
 * @desc    Delete stock movement by ID (reverses inventory changes)
 * @access  Public
 */
router.delete('/:id', validateIdParam, stockMovementController.deleteMovement);

module.exports = router;
