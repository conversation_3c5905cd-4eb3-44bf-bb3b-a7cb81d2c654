const express = require('express');
const router = express.Router();
const supplierController = require('../controllers/supplier.controller');

// Middleware for request logging
const requestLogger = (req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.originalUrl}`);
  next();
};

// Apply logging middleware to all routes
router.use(requestLogger);

// Validation middleware for supplier creation/update
const validateSupplierData = (req, res, next) => {
  const { name, email, phone, tax_number } = req.body;
  
  // Check required fields for POST requests
  if (req.method === 'POST') {
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'اسم المورد مطلوب',
        timestamp: new Date().toISOString()
      });
    }
  }
  
  // Validate email format if provided
  if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    return res.status(400).json({
      success: false,
      message: 'البريد الإلكتروني يجب أن يكون بصيغة صحيحة',
      timestamp: new Date().toISOString()
    });
  }
  
  // Validate phone format if provided
  if (phone && !/^\d{7,20}$/.test(phone.replace(/[\s\-\(\)]/g, ''))) {
    return res.status(400).json({
      success: false,
      message: 'رقم الهاتف يجب أن يحتوي على أرقام فقط (7-20 رقم)',
      timestamp: new Date().toISOString()
    });
  }
  
  // Validate tax_number length if provided
  if (tax_number && (tax_number.length < 5 || tax_number.length > 50)) {
    return res.status(400).json({
      success: false,
      message: 'الرقم الضريبي يجب أن يكون بين 5 و 50 حرف',
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

// Validation middleware for ID parameter
const validateIdParam = (req, res, next) => {
  const { id } = req.params;
  
  if (!id || isNaN(parseInt(id))) {
    return res.status(400).json({
      success: false,
      message: 'معرف المورد يجب أن يكون رقم صحيح',
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

// Routes

/**
 * @route   GET /api/suppliers
 * @desc    Get all suppliers with optional filtering and pagination
 * @access  Public
 * @params  ?page=1&limit=10&search=keyword&include_products=true
 */
router.get('/', supplierController.getAllSuppliers);

/**
 * @route   GET /api/suppliers/stats
 * @desc    Get suppliers statistics
 * @access  Public
 */
router.get('/stats', supplierController.getSuppliersStats);

/**
 * @route   GET /api/suppliers/:id
 * @desc    Get single supplier by ID
 * @access  Public
 * @params  ?include_products=true
 */
router.get('/:id', validateIdParam, supplierController.getSupplierById);

/**
 * @route   POST /api/suppliers
 * @desc    Create new supplier
 * @access  Public
 * @body    { name, contact_name?, phone?, email?, tax_number?, address? }
 */
router.post('/', validateSupplierData, supplierController.createSupplier);

/**
 * @route   PUT /api/suppliers/:id
 * @desc    Update supplier by ID
 * @access  Public
 * @body    { name?, contact_name?, phone?, email?, tax_number?, address? }
 */
router.put('/:id', validateIdParam, validateSupplierData, supplierController.updateSupplier);

/**
 * @route   DELETE /api/suppliers/:id
 * @desc    Delete supplier by ID
 * @access  Public
 */
router.delete('/:id', validateIdParam, supplierController.deleteSupplier);

module.exports = router;
