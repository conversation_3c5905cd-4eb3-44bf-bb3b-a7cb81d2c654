const express = require('express');
const router = express.Router();
const warehouseController = require('../controllers/warehouse.controller');

// Middleware for request logging
const requestLogger = (req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.originalUrl}`);
  next();
};

// Apply logging middleware to all routes
router.use(requestLogger);

// Validation middleware for warehouse creation/update
const validateWarehouseData = (req, res, next) => {
  const { name, code, capacity } = req.body;
  
  // Check required fields for POST requests
  if (req.method === 'POST') {
    if (!name || !code) {
      return res.status(400).json({
        success: false,
        message: 'اسم المستودع والكود مطلوبان',
        timestamp: new Date().toISOString()
      });
    }
  }
  
  // Validate code format if provided
  if (code && !/^[a-zA-Z0-9]+$/.test(code)) {
    return res.status(400).json({
      success: false,
      message: 'كود المستودع يجب أن يحتوي على أحرف وأرقام فقط',
      timestamp: new Date().toISOString()
    });
  }
  
  // Validate capacity if provided
  if (capacity !== undefined) {
    if (typeof capacity !== 'number' || capacity < 0) {
      return res.status(400).json({
        success: false,
        message: 'السعة يجب أن تكون رقم صحيح أكبر من أو يساوي 0',
        timestamp: new Date().toISOString()
      });
    }
  }
  
  next();
};

// Validation middleware for ID parameter
const validateIdParam = (req, res, next) => {
  const { id } = req.params;
  
  if (!id || isNaN(parseInt(id))) {
    return res.status(400).json({
      success: false,
      message: 'معرف المستودع يجب أن يكون رقم صحيح',
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

// Routes

/**
 * @route   GET /api/warehouses
 * @desc    Get all warehouses with optional filtering and pagination
 * @access  Public
 * @params  ?page=1&limit=10&search=keyword&min_capacity=100
 */
router.get('/', warehouseController.getAllWarehouses);

/**
 * @route   GET /api/warehouses/stats
 * @desc    Get warehouses statistics
 * @access  Public
 */
router.get('/stats', warehouseController.getWarehousesStats);

/**
 * @route   GET /api/warehouses/:id
 * @desc    Get single warehouse by ID
 * @access  Public
 */
router.get('/:id', validateIdParam, warehouseController.getWarehouseById);

/**
 * @route   POST /api/warehouses
 * @desc    Create new warehouse
 * @access  Public
 * @body    { name, code, location?, capacity?, description? }
 */
router.post('/', validateWarehouseData, warehouseController.createWarehouse);

/**
 * @route   PUT /api/warehouses/:id
 * @desc    Update warehouse by ID
 * @access  Public
 * @body    { name?, code?, location?, capacity?, description? }
 */
router.put('/:id', validateIdParam, validateWarehouseData, warehouseController.updateWarehouse);

/**
 * @route   DELETE /api/warehouses/:id
 * @desc    Delete warehouse by ID
 * @access  Public
 */
router.delete('/:id', validateIdParam, warehouseController.deleteWarehouse);

module.exports = router;
