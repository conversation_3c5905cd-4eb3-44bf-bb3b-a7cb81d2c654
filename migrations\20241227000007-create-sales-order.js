'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('sales_orders', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      order_number: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      customer_name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      customer_phone: {
        type: Sequelize.STRING,
        allowNull: true
      },
      status: {
        type: Sequelize.ENUM('NEW', 'PARTIALLY_DELIVERED', 'COMPLETED', 'CANCELLED'),
        allowNull: false,
        defaultValue: 'NEW'
      },
      total_amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      order_date: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      delivery_date: {
        type: Sequelize.DATE,
        allowNull: true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('sales_orders', ['order_number'], {
      name: 'sales_orders_order_number_index',
      unique: true
    });

    await queryInterface.addIndex('sales_orders', ['customer_name'], {
      name: 'sales_orders_customer_name_index'
    });

    await queryInterface.addIndex('sales_orders', ['customer_phone'], {
      name: 'sales_orders_customer_phone_index'
    });

    await queryInterface.addIndex('sales_orders', ['status'], {
      name: 'sales_orders_status_index'
    });

    await queryInterface.addIndex('sales_orders', ['order_date'], {
      name: 'sales_orders_order_date_index'
    });

    await queryInterface.addIndex('sales_orders', ['delivery_date'], {
      name: 'sales_orders_delivery_date_index'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('sales_orders');
  }
};
