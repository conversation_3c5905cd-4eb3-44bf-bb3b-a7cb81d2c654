'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('inventory', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      product_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'products',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      warehouse_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'warehouses',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      quantity: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      minimum_quantity: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      last_checked_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add unique constraint for product_id + warehouse_id combination
    await queryInterface.addConstraint('inventory', {
      fields: ['product_id', 'warehouse_id'],
      type: 'unique',
      name: 'inventory_product_warehouse_unique'
    });

    // Add indexes
    await queryInterface.addIndex('inventory', ['product_id'], {
      name: 'inventory_product_id_index'
    });

    await queryInterface.addIndex('inventory', ['warehouse_id'], {
      name: 'inventory_warehouse_id_index'
    });

    await queryInterface.addIndex('inventory', ['quantity'], {
      name: 'inventory_quantity_index'
    });

    await queryInterface.addIndex('inventory', ['minimum_quantity'], {
      name: 'inventory_minimum_quantity_index'
    });

    await queryInterface.addIndex('inventory', ['last_checked_at'], {
      name: 'inventory_last_checked_at_index'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('inventory');
  }
};
